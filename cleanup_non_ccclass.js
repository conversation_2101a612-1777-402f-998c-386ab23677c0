const fs = require('fs');
const path = require('path');

// 读取精确分析结果
function loadPreciseAnalysis() {
    try {
        const data = fs.readFileSync('ccclass_precise_analysis.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('无法读取精确分析结果文件:', error.message);
        return null;
    }
}

// 创建备份文件夹
function createBackupDirectory() {
    const backupDir = './scripts_ccs_backup';
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        console.log(`创建备份文件夹: ${backupDir}`);
    }
    return backupDir;
}

// 移动文件到备份文件夹
function moveToBackup(fileName, backupDir) {
    const sourcePath = path.join('./scripts_ccs', fileName);
    const backupPath = path.join(backupDir, fileName);
    
    try {
        fs.renameSync(sourcePath, backupPath);
        return true;
    } catch (error) {
        console.error(`移动文件失败: ${fileName}`, error.message);
        return false;
    }
}

// 主函数
function main() {
    const analysisResult = loadPreciseAnalysis();
    if (!analysisResult) {
        return;
    }
    
    const backupDir = createBackupDirectory();
    const nonCcclassFiles = analysisResult.definitelyNotCcclassFiles;
    
    console.log(`\n开始清理 ${nonCcclassFiles.length} 个确定不是 ccclass 的文件...\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    nonCcclassFiles.forEach((fileInfo, index) => {
        const fileName = fileInfo.fileName;
        
        console.log(`[${index + 1}/${nonCcclassFiles.length}] 移动到备份: ${fileName} (${fileInfo.className})`);
        
        if (moveToBackup(fileName, backupDir)) {
            successCount++;
            console.log(`  ✓ 成功移动到备份文件夹`);
        } else {
            failCount++;
            console.log(`  ✗ 移动失败`);
        }
    });
    
    console.log(`\n=== 清理完成 ===`);
    console.log(`成功移动到备份: ${successCount} 个文件`);
    console.log(`失败: ${failCount} 个文件`);
    
    // 显示剩余的文件
    const scriptsDir = './scripts_ccs';
    if (fs.existsSync(scriptsDir)) {
        const remainingFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.js'));
        console.log(`\nscripts_ccs 文件夹中剩余 ${remainingFiles.length} 个文件:`);
        
        // 分类显示剩余文件
        const probablyCcclass = analysisResult.probablyCcclassFiles.map(f => f.fileName);
        const uncertain = analysisResult.uncertainFiles.map(f => f.fileName);
        
        console.log(`\n=== 可能是 ccclass 的文件 (${analysisResult.probablyCcclassFiles.length} 个) ===`);
        analysisResult.probablyCcclassFiles.forEach(file => {
            if (remainingFiles.includes(file.fileName)) {
                console.log(`✓ ${file.fileName} - ${file.className} (分数: ${file.ccclassScore})`);
            }
        });
        
        console.log(`\n=== 不确定的文件 (${analysisResult.uncertainFiles.length} 个) ===`);
        analysisResult.uncertainFiles.forEach(file => {
            if (remainingFiles.includes(file.fileName)) {
                console.log(`? ${file.fileName} - ${file.className}`);
            }
        });
    }
    
    // 生成清理报告
    const report = {
        timestamp: new Date().toISOString(),
        cleanedFiles: nonCcclassFiles,
        backupDirectory: backupDir,
        successCount: successCount,
        failCount: failCount,
        remainingProbablyCcclass: analysisResult.probablyCcclassFiles.length,
        remainingUncertain: analysisResult.uncertainFiles.length
    };
    
    fs.writeFileSync('cleanup_report.json', JSON.stringify(report, null, 2));
    console.log(`\n清理报告已保存到: cleanup_report.json`);
}

main();
