var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2DragonBody = require("DragonBody");
var $2OrganismBase = require("OrganismBase");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2MCBoss = require("MCBoss");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MTideDefendRmod = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.nodeArr1 = [];
    t._myData = null;
    t.roleId = 30400;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return .5;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
          o.mySkeleton.reset(e);
          o.setAnimation("idle", true);
          o.scheduleOnce(function () {
            o.onNewSize(o.roleNode.getContentSize());
          });
        })));
      }
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.mulSelf(.7);
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
  };
  _ctor.prototype.setRole = function () {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    var e = $2Cfg.Cfg.Role.find({
      roleId: this.roleId
    });
    this.property.set(e);
    this.updateProperty();
    this.skillMgr.add(this.myData.startSkill, true);
    this.forwardDirection.set(cc.v2(0, 100));
    this.initHp();
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.game.showDamageDisplay(e, this.haedPosition);
      if (this.curHp <= 0) {
        this.toDead();
        wonderSdk.vibrate(0);
      }
      return e;
    }
  };
  _ctor.prototype.materialTwinkle = function () {};
  _ctor.prototype.toDead = function () {
    if (!this.isDead) {
      this.isDead = true;
      this.game.sendEvent("BatchFail");
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
    }
  };
  _ctor.prototype.onSkill = function () {
    this.game.showEffectByPath("bones/fx_spark", {
      nodeAttr: {
        position: cc.v2(this.haedPosition.x, this.haedPosition.y + 15)
      },
      spAttr: {
        animation: "fx"
      }
    });
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t = e.comp;
    if (t instanceof $2DragonBody.default && t.curIndex) {
      var o = t.owerChains.getHurt();
      this.behit(o);
    } else {
      t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt());
    }
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.M34_GetBuff, this.onGetBuff, this);
  };
  _ctor.prototype.onGetBuff = function () {
    var e = cc.v2(this.position.x, this.position.y);
    this.game.showEffectByPath("bones/skill/fx_buff_ad", {
      nodeAttr: {
        position: e,
        scale: 1
      },
      spAttr: {
        animation: "animation"
      },
      delayRemove: 1
    });
  };
  cc__decorate([ccp_property([cc.Node])], _ctor.prototype, "nodeArr1", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeTideDefend/MTideDefendRmod")], _ctor);
}($2OrganismBase.default);
exports.default = def_MTideDefendRmod;