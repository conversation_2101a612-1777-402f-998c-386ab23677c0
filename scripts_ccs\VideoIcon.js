var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_VideoIcon = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
    this.resetState();
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetState, this);
  };
  Object.defineProperty(_ctor.prototype, "hasADCoupons", {
    get: function () {
      return $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) + $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.resetState = function () {
    var e;
    if ((e = this.hasADCoupons ? $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out : $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) != this.curState) {
      this.curState = e;
      var t = $2Cfg.Cfg.CurrencyConfig.get(e);
      $2Manager.Manager.loader.loadSpriteToSprit(t.icon, this.node.getComponent(cc.Sprite));
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/VideoIcon")], _ctor);
}(cc.Component);
exports.default = def_VideoIcon;