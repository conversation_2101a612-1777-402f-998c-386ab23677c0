var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20_ShopPartItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.contentnode = null;
    t.title = null;
    t.data = null;
    t.cloneitem = null;
    t.content = [];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setData = function (e) {
    var t = this;
    this.data = e;
    this.cloneitem || $2Manager.Manager.loader.loadPrefab(e.prefabe).then(function (e) {
      t.cloneitem = e;
      t.refreshData();
    });
  };
  _ctor.prototype.resetView = function () {
    this.title.string = this.data.title;
  };
  _ctor.prototype.refreshData = function () {
    this.content = this.getList();
    this.resetView();
  };
  _ctor.prototype.getList = function () {
    return [];
  };
  _ctor.prototype.refresh = function () {};
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetView, this);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "contentnode", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "title", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_M20_ShopPartItem;