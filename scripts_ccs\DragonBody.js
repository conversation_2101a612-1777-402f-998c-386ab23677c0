var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2FCollider = require("FCollider");
var $2Monster = require("Monster");
var $2ModeChainsModel = require("ModeChainsModel");
var $2Buff = require("Buff");
var $2Cfg = require("Cfg");
var $2GameUtil = require("GameUtil");
var $2FCircleCollider = require("FCircleCollider");
var $2Game = require("Game");
var $2GameSeting = require("GameSeting");
var $2Intersection = require("Intersection");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc__decorator.menu;
var S = cc.v2();
var k = cc.v2();
var def_DragonBody = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.roleDir = 1;
    t.isRetreat = false;
    t.itemList = [];
    t.malss = [];
    t._curIndex = 0;
    t.oldIndex = 0;
    t.middleIndex = 0;
    t.targetAngle = 0;
    t.twinkleDt = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return 1;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "jointNum", {
    get: function () {
      return this.owerChains.jointNum;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    this.listBox = this.node.getORaddChildByName("List");
    this.botEffectBox = this.node.getORaddChildByName("botEffectBox").setAttribute({
      y: 9999
    });
    this.topEffectBox = this.node.getORaddChildByName("topEffectBox").setAttribute({
      y: 9999
    });
    e.prototype.onLoad.call(this);
  };
  _ctor.prototype.init = function () {
    var t = this;
    e.prototype.init.call(this);
    this.resetList();
    this.game.createLifeLabel(this, {
      active: false
    }).then(function (e) {
      t.LifeLabel = e;
      e.changeListener(false);
      t.LifeLabel.string = 1 == t.curHp ? "" : "" + t.curHp;
    });
  };
  _ctor.prototype.setAmClip = function () {};
  Object.defineProperty(_ctor.prototype, "size", {
    get: function () {
      return this.itemList.length;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onDestroy = function () {
    this.itemList.length = 0;
    this.malss.length = 0;
  };
  _ctor.prototype.resetList = function () {
    var e;
    this.itemList.length = 0;
    this.malss.length = 0;
    this.isRetreat = false;
    this.roleNode.removeAllChildren();
    var t = cc.misc.clampf(this.lvCfg.hp, 1, this.jointNum);
    this.isTail && (t = 1);
    for (var o = 0; o < t; o++) {
      var i = this.listBox.children[o] || cc.instantiate(this.listBox.children[0]);
      var n = null === (e = i.getComponent($2FCircleCollider.default)) || undefined === e ? undefined : e.setActive(false);
      if (o % 3 != 0) {
        null == n || n.destroy();
      } else {
        n.radius = 70;
      }
      i.setAttribute({
        active: false,
        scale: this.monCfg.Scale,
        parent: this.listBox,
        y: 9999
      });
      this.itemList.push(i);
      var r = i.getComponent(cc.Sprite);
      $2Manager.Manager.loader.loadSpriteToSprit(this.monCfg.img, r, this.node);
      var a = r.getMaterial(0);
      this.malss.push(a);
      a.setProperty("setwhite", 0);
    }
    this.middleIndex = Math.max(Math.floor(this.size / 2) - 1, 0);
    this.roleNode.getComponent(cc.Sprite).spriteFrame = null;
    this.roleNode.active = false;
  };
  Object.defineProperty(_ctor.prototype, "curIndex", {
    get: function () {
      return this._curIndex;
    },
    set: function (e) {
      e > this._curIndex && (this.oldIndex = this._curIndex);
      this._curIndex = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerState = function () {};
  _ctor.prototype.toIdle = function () {};
  _ctor.prototype.toMove = function () {};
  _ctor.prototype.toBeHit = function () {};
  _ctor.prototype.toDead = function () {
    var e = this;
    $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_dragonhit);
    this.isDead = true;
    var t = this.size - 1;
    this.itemList.forEach(function (o, i) {
      var n = o.position.clone();
      $2Game.Game.timerOnce(function () {
        e.game.showEffectByPath("bones/skill/fx_draw", {
          nodeAttr: {
            position: n,
            parent: e.game._topEffectNode
          },
          spAttr: {
            defaultAnimation: "animation",
            premultipliedAlpha: false
          },
          delayRemove: .2
        });
      }, .05 * (t - i));
    });
    this.node.emit($2ListenID.ListenID.Fight_Dead);
    this.droppedItems();
    this.owerChains.onKillMonster(this);
  };
  _ctor.prototype.unuse = function () {
    var t;
    null === (t = this.LifeLabel) || undefined === t || t.remove();
    this.LifeLabel = null;
    this.listBox.hideAllChildren();
    this.owerChains = null;
    e.prototype.unuse.call(this);
  };
  Object.defineProperty(_ctor.prototype, "position", {
    get: function () {
      var e;
      return (null === (e = this.itemList[this.middleIndex]) || undefined === e ? undefined : e.position) || cc.v2(0, 9999);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bodyPosition", {
    get: function () {
      return this.position;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "haedPosition", {
    get: function () {
      return this.position;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isInvincible", {
    get: function () {
      var e;
      return this.curIndex < 0 || (null === (e = this.buffMgr) || undefined === e ? undefined : e.isInvincible);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxSpeed", {
    get: function () {
      return this.owerChains.maxSpeed;
    },
    set: function (e) {
      this._maxSpeed = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (t) {
    var o;
    var i;
    var n;
    var r;
    var a = this;
    if (this.isActive) {
      null === (o = this.LifeLabel) || undefined === o || o.node.setActive(null === (i = this.itemList[this.middleIndex]) || undefined === i ? undefined : i.active);
      if (null === (n = this.itemList[this.middleIndex]) || undefined === n ? undefined : n.active) {
        k.set(this.itemList[this.middleIndex].position), null === (r = this.LifeLabel) || undefined === r || r.onOwerUpdate(0), this.botEffectBox.setPosition(k), this.topEffectBox.setPosition(k), this.roleNode.setPosition(k), this.setDroppedItem(), this.roleNode.setActive(true);
      }
      this.twinkleNum > 0 && (this.twinkleNum -= t);
      this.itemList.forEach(function (e, o) {
        var i;
        var n;
        var r = a.owerChains.findPos(a.curIndex + o * a.owerChains.lineInterval);
        if (r) {
          S.setVal(r.x, r.y);
          if (e.active) {
            if (!a.owerChains.isBanMove) {
              var s = cc.Vec2.squaredDistance(S, e.position);
              if (s > 300 && !a.isRetreat) {
                if (a.owerChains.isBodyRotate) {
                  var c = $2GameUtil.GameUtil.GetAngle(S, e.position) + 90;
                  e.angle = $2Intersection.AngleSlerp.slerp(e.angle, c, 2 * t);
                } else {
                  var l = e.position.x - S.x;
                  if (Math.abs(l) > 0) {
                    var u = l > 0 ? -1 : 1;
                    var p = a.monCfg.Scale * u;
                    e.scaleX = p;
                  }
                }
              }
              if (s < 64e4) {
                S.set(S.sub(e.position).normalize());
                cc.Vec2.multiplyScalar(S, S, Math.min(a.maxSpeed + s / a.owerChains.retreatDifference, 600) * t);
                cc.Vec2.add(S, e.position, S);
              }
              e.setPosition(S);
            }
            null === (n = a.malss[o]) || undefined === n || n.setProperty("setwhite", a.twinkleNum);
          } else {
            e.setPosition(S);
            null === (i = e.getComponent($2FCollider.default)) || undefined === i || i.setActive(true);
          }
        }
        e.setActive(!!r);
      });
      this.node.opacity = this.curIndex > 0 ? 255 : 0;
    }
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.materialTwinkle = function () {
    var e = this;
    this.twinkleNum = .6;
    this.malss.forEach(function (t) {
      return t.setProperty("setwhite", e.twinkleNum);
    });
  };
  _ctor.prototype.behit = function (e) {
    var t = this;
    if (!this.isDead && !this.isInvincible && this.hurtMgr.checkHurt(e)) {
      var o = e.val;
      this.curHp -= o;
      this.LifeLabel && (this.LifeLabel.string = Math.ceil(this.curHp < 0 ? 0 : this.curHp));
      this.game.showDamageDisplay(e, this.position);
      this.materialTwinkle();
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      if (this.curHp <= 0) {
        this.toDead(e);
      } else {
        this.toBeHit(e.hitBack);
      }
      this.game.bulletList.size < 50 && this.itemList.forEach(function (e) {
        $2Game.Game.tween(e).stopLast().to(.05, {
          scaleY: 1.1 * t.monCfg.Scale
        }).to(.2, {
          scaleY: t.monCfg.Scale
        }).start();
      });
      return e;
    }
  };
  _ctor.prototype.setDroppedItem = function () {
    if (!this.roleNode.active && this.lvCfg.dropExpRatio) {
      var e = this.lvCfg.dropExpRatio[0][0];
      var t = $2Cfg.Cfg.CurrencyConfig.get(e);
      var o = t.icon;
      if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect) {
        o = "v1/images/fight/icon_tcs_bx0" + this.lvCfg.dropExpRatio[0][3];
      } else if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
        var i = this.lvCfg.dropExpRatio[0][3];
        var n = $2Game.ModeCfg.Buff.get(i);
        o = $2GameSeting.GameSeting.getRarity(n.rarity).bubbleBg;
        new cc.Node("msg").setAttribute({
          parent: this.roleNode
        }).addComponent(cc.RichText).setAttribute({
          string: n.desc,
          fontSize: 36,
          lineHeight: 38,
          maxWidth: 180,
          horizontalAlign: cc.macro.TextAlignment.CENTER
        });
        this.LifeLabel.offset = cc.v2(0, -80);
      } else {
        t.type == $2GameSeting.GameSeting.GoodsType.RandomBuffDrop && (this.LifeLabel.offset = cc.v2(0, -80));
      }
      $2Manager.Manager.loader.loadSpriteToSprit(o, this.roleNode.getComponent(cc.Sprite), this.game.gameNode);
    }
  };
  _ctor.prototype.droppedItems = function () {
    var e = this;
    this.lvCfg.dropExpRatio && this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (t) {
      if (t.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect) {
        var o = t.param;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
          type: o,
          getPool: function (t) {
            return e.mode.fightBuffWidth(o, t);
          }
        }).setDailyTime(.3));
      } else if (t.type == $2GameSeting.GameSeting.GoodsType.BuffDropSelect) {
        e.createGoods(t);
      } else if (t.type == $2GameSeting.GameSeting.GoodsType.OpenBuffSelect) {
        var i = t.param;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
          type: i,
          isBinding: true,
          getPool: function (t) {
            return e.mode.fightBuffWidth(i, t, $2Cfg.Cfg.PoolList.get(i).Pool);
          }
        }).setDailyTime(.3));
      } else if (t.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
        var n = $2Game.ModeCfg.Buff.get(t.param);
        t.icon = n.icon;
        e.createGoods(t);
      } else if (t.type == $2GameSeting.GameSeting.GoodsType.RandomBuffDrop) {
        var r = $2GameUtil.GameUtil.getRandomByWeightInArray($2Cfg.Cfg.PoolList.get(t.param).Pool, 1)[0];
        n = $2Game.ModeCfg.Buff.get(r);
        t.icon = n.icon;
        t.id = $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop;
        t.param = r;
        e.createGoods(t);
      } else {
        var s = $2Cfg.Cfg.CurrencyConfig.get(t.id);
        e.game.knapsackMgr.addGoods(t.id, t.num, s.type);
      }
    });
  };
  _ctor.prototype.createGoods = function (e) {
    var t = this;
    this.game.createGoods(e, this.position).then(function (e) {
      var o = t.game.mainRole.bodyPosition;
      $2Game.Game.tween(e.node).set({
        active: true,
        scale: 0,
        opacity: 255
      }).to(.05, {
        scale: 1.5
      }).parallel(cc.tween().to(.3, {
        scale: 1
      }), cc.tween().by(.6, {
        x: $2Manager.Manager.random.randomInt(-100, 100)
      }, {
        easing: cc.easing.sineOut
      }), cc.tween().by(.2, {
        y: 80
      }, {
        easing: cc.easing.sineOut
      }).to(2, {
        y: o.y
      }, {
        easing: cc.easing.bounceOut
      })).start();
    });
  };
  _ctor.prototype.addSkill = function (e, t, o) {
    undefined === t && (t = true);
    undefined === o && (o = false);
    return null;
  };
  _ctor.prototype.addBuff = function (t, o) {
    var i = $2Game.ModeCfg.Buff.get(t);
    if (i && $2GameUtil.GameUtil.hasIntersection(i.attr, $2Buff.Buff.MoveAtr)) {
      return this.owerChains.addBuff(t, o);
    } else {
      return e.prototype.addBuff.call(this, t, o);
    }
  };
  _ctor.prototype.addBuffByData = function (t, o) {
    undefined === o && (o = 1);
    if (t && $2GameUtil.GameUtil.hasIntersection(t.attr, $2Buff.Buff.MoveAtr)) {
      return this.owerChains.addBuffByData(t, o);
    } else {
      return e.prototype.addBuffByData.call(this, t, o);
    }
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2Monster.Monster);
exports.default = def_DragonBody;