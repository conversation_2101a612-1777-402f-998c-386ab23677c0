const fs = require('fs');
const path = require('path');

// 获取两个文件夹中的文件列表
function getFileList(dirPath) {
    if (!fs.existsSync(dirPath)) {
        return [];
    }
    return fs.readdirSync(dirPath).filter(file => file.endsWith('.js'));
}

// 删除文件
function deleteFile(filePath) {
    try {
        fs.unlinkSync(filePath);
        return true;
    } catch (error) {
        console.error(`删除文件失败: ${filePath}`, error.message);
        return false;
    }
}

// 主函数
function main() {
    const scripts5Dir = './scripts_5';
    const scriptsCcsDir = './scripts_ccs';
    
    // 检查文件夹是否存在
    if (!fs.existsSync(scripts5Dir)) {
        console.log('scripts_5 文件夹不存在');
        return;
    }
    
    if (!fs.existsSync(scriptsCcsDir)) {
        console.log('scripts_ccs 文件夹不存在');
        return;
    }
    
    // 获取文件列表
    const scripts5Files = getFileList(scripts5Dir);
    const scriptsCcsFiles = getFileList(scriptsCcsDir);
    
    console.log(`scripts_5 文件夹包含 ${scripts5Files.length} 个 JS 文件`);
    console.log(`scripts_ccs 文件夹包含 ${scriptsCcsFiles.length} 个 JS 文件`);
    
    // 找出需要删除的文件（在 scripts_5 中且也在 scripts_ccs 中的文件）
    const filesToDelete = scripts5Files.filter(file => scriptsCcsFiles.includes(file));
    
    console.log(`\n发现 ${filesToDelete.length} 个重复文件需要从 scripts_5 中删除:\n`);
    
    if (filesToDelete.length === 0) {
        console.log('没有发现重复文件，无需删除。');
        return;
    }
    
    // 显示将要删除的文件列表
    console.log('=== 将要删除的文件列表 ===');
    filesToDelete.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
    });
    
    console.log(`\n开始删除 scripts_5 中的重复文件...\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    // 删除重复文件
    filesToDelete.forEach((fileName, index) => {
        const filePath = path.join(scripts5Dir, fileName);
        
        console.log(`[${index + 1}/${filesToDelete.length}] 删除: ${fileName}`);
        
        if (deleteFile(filePath)) {
            successCount++;
            console.log(`  ✓ 成功删除`);
        } else {
            failCount++;
            console.log(`  ✗ 删除失败`);
        }
    });
    
    console.log(`\n=== 删除完成 ===`);
    console.log(`成功删除: ${successCount} 个文件`);
    console.log(`删除失败: ${failCount} 个文件`);
    
    // 显示删除后的统计
    const remainingFiles = getFileList(scripts5Dir);
    console.log(`\nscripts_5 文件夹现在剩余 ${remainingFiles.length} 个 JS 文件`);
    
    // 生成删除报告
    const report = {
        timestamp: new Date().toISOString(),
        originalScripts5Count: scripts5Files.length,
        scriptsCcsCount: scriptsCcsFiles.length,
        duplicatesFound: filesToDelete.length,
        successfullyDeleted: successCount,
        failedToDelete: failCount,
        remainingInScripts5: remainingFiles.length,
        deletedFiles: filesToDelete,
        remainingFiles: remainingFiles
    };
    
    fs.writeFileSync('scripts5_cleanup_report.json', JSON.stringify(report, null, 2));
    console.log(`\n删除报告已保存到: scripts5_cleanup_report.json`);
    
    // 显示一些剩余文件的示例
    if (remainingFiles.length > 0) {
        console.log(`\n=== scripts_5 中剩余文件示例 (前10个) ===`);
        remainingFiles.slice(0, 10).forEach(file => {
            console.log(`- ${file}`);
        });
        if (remainingFiles.length > 10) {
            console.log(`... 还有 ${remainingFiles.length - 10} 个文件`);
        }
    }
    
    console.log(`\n✅ 分离完成！scripts_ccs 包含 ${scriptsCcsFiles.length} 个单 ccclass 文件，scripts_5 包含 ${remainingFiles.length} 个其他文件。`);
}

main();
