var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2Intersection = require("Intersection");
var $2GameUtil = require("GameUtil");
var $2Monster = require("Monster");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2MMGuards = require("MMGuards");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MMGMonster = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.line = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    var t = $2Cfg.Cfg.Monster.get(this.lvCfg.monId[0]);
    t.skill && this.skillMgr.add(t.skill[0], false);
    this.knifeController = new $2MMGuards.MMGuards.KnifeController().setAttribute({
      ower: this
    });
  };
  _ctor.prototype.setRotateData = function (e) {
    this._rotateData = e;
  };
  _ctor.prototype.setAmClip = function () {
    var e = this;
    this.mySkeleton.skeletonData = null;
    $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
      e.mySkeleton.reset(t);
      e.playAction("idle", true);
      e.mySkeleton.setCompleteListener(function () {
        "attack" == e.mySkeleton.animation && e.playAction("idle", true);
      });
      e.delayByGame(function () {
        e.onNewSize(e.roleNode.getContentSize());
      });
    });
  };
  _ctor.prototype.onNewSize = function (e) {
    var t;
    e.mulSelf(1);
    this.node.setContentSize(e.width, e.height);
    this.collider.size = e;
    this.collider.offset = cc.v2(0, e.height / 2);
    this.radius = .5 * e.width;
    this._haedPosition.setVal(0, e.height * this.scale);
    this._bodyPosition.setVal(0, e.height * this.scale / 2);
    null === (t = this.skillMgr) || undefined === t || t.launchPoint.set(this._bodyPosition);
  };
  _ctor.prototype.registerState = function () {};
  _ctor.prototype.toIdle = function () {};
  _ctor.prototype.behit = function (t) {
    this.mySkeleton.playQueue(["hit", "idle"], true);
    this.curHp = 0;
    return e.prototype.behit.call(this, t);
  };
  _ctor.prototype.toBeHit = function () {};
  _ctor.prototype.toDead = function () {
    var e = this;
    if (!this.isDead) {
      this.isDead = true;
      $2Time.Time.delay(.5, function () {
        e.game.showEntityDieEffect(2, {
          position: e.position,
          scale: e.monCfg.Scale
        });
        cc.tween(e.node).parallel(cc.tween().to(.1, {
          angle: -30
        }), cc.tween().bezierTo(.4, e.position, e.position.add(cc.v2(400, 300)), e.position.add(cc.v2(800, 0)))).start();
      });
    }
  };
  _ctor.prototype.setLine = function (e) {
    var t = this;
    var o = (e * this._rotateData.rotateDirection + 180 + this._rotateData.startAngle) % 360;
    this.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o % 360));
    var i = this.bodyPosition;
    var n = $2GameUtil.GameUtil.AngleAndLenToPos(o, 2e3).add(this.bodyPosition);
    var r = [];
    this.game.physicsPolygonColliders.forEach(function (e) {
      e.tag != t.node.nodeTag && e.points.forEach(function (t, o) {
        var a;
        var s;
        var c = $2Intersection.Intersection.getLineSegmentIntersection(i, n, t.add(e.offset), (null === (a = e.points[o + 1]) || undefined === a ? undefined : a.add(e.offset)) || (null === (s = e.points[0]) || undefined === s ? undefined : s.add(e.offset)));
        c && r.push({
          pos: c,
          d: cc.Vec2.squaredDistance(i, c)
        });
      });
    });
    r.sort(function (e, t) {
      return e.d - t.d;
    });
    r[0] && n.set(r[0].pos);
    this.line.setAttribute({
      parent: this.game.botEffectNode,
      active: true,
      position: this.bodyPosition,
      angle: o,
      height: cc.Vec2.distance(i, n)
    });
  };
  _ctor.prototype.hideLine = function () {
    this.line.setActive(false);
  };
  cc__decorate([ccp_property({
    type: cc.Node,
    displayName: "瞄准辅助线"
  })], _ctor.prototype, "line", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeManGuards/MMGMonster")], _ctor);
}($2Monster.Monster);
exports.default = def_MMGMonster;