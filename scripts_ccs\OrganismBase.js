var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.nullMap = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2GameatrCfg = require("GameatrCfg");
var $2Notifier = require("Notifier");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2Buff = require("Buff");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2BaseEntity = require("BaseEntity");
var $2MoveEntity = require("MoveEntity");
exports.nullMap = new $2GameSeting.TMap();
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_OrganismBase = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.LifeBar = null;
    t._stateMachine = null;
    t.forwardDirection = cc.Vec2.ZERO;
    t._level = 1;
    t._nextLevelExp = 60;
    t._levelExp = 1;
    t._curHp = 0;
    t.curHpProgress = 1;
    t._curArmor = 0;
    t._dt = 0;
    t._logicTime = .1;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "canSkill", {
    get: function () {
      return this.game.canSkill;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "canFire", {
    get: function () {
      return true;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "firstSkill", {
    get: function () {
      var e;
      if (null === (e = this.skillMgr) || undefined === e) {
        return undefined;
      } else {
        return e.skills[0];
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isBanMove", {
    get: function () {
      var e;
      return (null === (e = this.buffMgr) || undefined === e ? undefined : e.isBanMove) || false;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isInvincible", {
    get: function () {
      var e;
      if (null === (e = this.buffMgr) || undefined === e) {
        return undefined;
      } else {
        return e.isInvincible;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "curStateTag", {
    get: function () {
      return this._stateMachine.curStateTag;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "fsm", {
    get: function () {
      return this._stateMachine;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "parent", {
    get: function () {
      return this._parent;
    },
    set: function (e) {
      var t;
      var o;
      null === (o = null === (t = this._parent) || undefined === t ? undefined : t.petList) || undefined === o || o.delete(this);
      e && e.petList.push(this);
      this._parent = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "curHp", {
    get: function () {
      return this._curHp;
    },
    set: function (e) {
      this._curHp = Math.min(this.property.cut.hp, e);
      this.curHpProgress = this._curHp / this.property.cut.hp;
      this.LifeBar && (this.LifeBar.fillRange = this.curHpProgress);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "curArmor", {
    get: function () {
      return this._curArmor;
    },
    set: function (e) {
      this._curArmor = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initHp = function () {
    var e;
    this.curHp = this.property.cut.hp;
    null === (e = this.roleNode) || undefined === e || e.setActive(true);
  };
  _ctor.prototype.init = function () {
    var t;
    e.prototype.init.call(this);
    null === (t = this.node) || undefined === t || t.setActive(true);
  };
  _ctor.prototype.updateProperty = function () {
    var e;
    null === (e = this.property) || undefined === e || e.updateVo();
  };
  _ctor.prototype.removeEffectBox = function () {
    if (this.botEffectBox) {
      for (var e = this.botEffectBox.childrenCount - 1; e >= 0; e--) {
        if (o = (t = this.botEffectBox.children[e]).getComponent($2BaseEntity.default)) {
          o.removeEntityToUpdate();
        } else {
          t.destroy();
        }
      }
    }
    if (this.topEffectBox) {
      for (e = this.topEffectBox.childrenCount - 1; e >= 0; e--) {
        var t;
        var o;
        if (o = (t = this.topEffectBox.children[e]).getComponent($2BaseEntity.default)) {
          o.removeEntityToUpdate();
        } else {
          t.destroy();
        }
      }
    }
  };
  _ctor.prototype.resetCollider = function (e) {
    this.collider.size = cc.size(e, e);
  };
  _ctor.prototype.behit = function (e) {
    if (this.isDead) {
      return null;
    } else {
      if (this.hurtMgr.checkHurt(e)) {
        return this.curHp -= e.val, this.materialTwinkle(), e;
      } else {
        return null;
      }
    }
  };
  _ctor.prototype.treat = function (e) {
    var t = e.baseVal;
    t *= 1 + this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.hpPer, 0);
    this.curHp += t;
    $2AlertManager.AlertManager.showHurtTips(Math.round(t), {
      position: this.bodyPosition,
      color: cc.Color.GREEN
    });
  };
  _ctor.prototype.addArmor = function (e, t) {
    undefined === t && (t = true);
    this.curArmor += e;
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
    this.node.changeListener(t, $2ListenID.ListenID.Fight_OnBuff, this.OnBuff, this);
    this.node.changeListener(t, $2ListenID.ListenID.Fight_OnSkill, this.onSkill, this);
  };
  _ctor.prototype.onGameState = function (e) {
    var t;
    var o;
    var i;
    var n;
    if (e == $2Game.Game.State.PAUSE) {
      null === (t = this.myAm) || undefined === t || t.pause();
      null === (o = this.mySkeleton) || undefined === o || o.setPause(true);
    } else {
      null === (i = this.myAm) || undefined === i || i.resume();
      null === (n = this.mySkeleton) || undefined === n || n.setPause(false);
    }
  };
  _ctor.prototype.materialTwinkle = function () {
    this._mals;
  };
  _ctor.prototype.setAnimation = function (e, t) {
    undefined === t && (t = false);
    if (!t || this.mySkeleton.animation != e) {
      return this.mySkeleton.setAnimation(0, e, t);
    }
  };
  _ctor.prototype.onLoad = function () {
    var t;
    var o;
    var i;
    e.prototype.onLoad.call(this);
    this.roleNode = this.node.getChildByName("role");
    this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
    this._mals = (null === (t = this.mySkeleton) || undefined === t ? undefined : t.getMaterial(0)) || (null === (i = null === (o = this.roleNode) || undefined === o ? undefined : o.getComponent(cc.Sprite)) || undefined === i ? undefined : i.getMaterial(0));
  };
  _ctor.prototype.onEnable = function () {
    e.prototype.onEnable.call(this);
    this.mySkeleton && this.game && (this.mySkeleton.paused = this.game.gameState == $2Game.Game.State.PAUSE);
  };
  _ctor.prototype.onNewSize = function () {};
  _ctor.prototype.onUpdate = function (t) {
    var o;
    var i;
    var n;
    var r;
    if (this.isActive && (this._dt += t) > this._logicTime && this.canSkill) {
      null === (o = this.buffMgr) || undefined === o || o.onUpdate(this._dt);
      null === (i = this.skillMgr) || undefined === i || i.onUpdate(this._dt);
      null === (n = this.hurtMgr) || undefined === n || n.onUpdate(this._dt);
      this._dt = 0;
    }
    this.node.emit($2ListenID.ListenID.Fight_EntityUpdate, t);
    null === (r = this._stateMachine) || undefined === r || r.update(t);
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.OnBuff = function (e) {
    var t;
    var o = this.property.cut.hp;
    var i = this.curHpProgress;
    this.updateProperty();
    null === (t = this.skillMgr) || undefined === t || t.onBuff(e);
    if (null == e ? undefined : e.attr.includes($2GameatrCfg.GameatrDefine.hp)) {
      var n = this.property.cut.hp - o;
      if (n > 0) {
        this.curHp += n * i;
      } else {
        this.curHp = Math.min(this.curHp, this.property.cut.hp);
      }
    }
  };
  _ctor.prototype.onSkill = function () {};
  _ctor.prototype.onKill = function () {};
  _ctor.prototype.setAmClip = function () {};
  _ctor.prototype.getAmClip = function (e, t, o, i) {
    var n = this;
    undefined === i && (i = 10);
    return new Promise(function (r) {
      n.myAm || (n.myAm = n.roleNode.getORaddComponent(cc.Animation));
      o = JSON.parse(o);
      n.myAmName = e;
      var a = [];
      var s = o[1].split("-");
      for (var c = +s[0]; c <= +s[1]; c++) {
        a.push("" + o[0] + c);
      }
      $2Game.Game.Mgr.instance.getAmClip({
        assetName: e + "_" + t,
        amName: t,
        path: a,
        frame_time: i
      }).then(function (e) {
        e.wrapMode = cc.WrapMode.Loop;
        n.myAm.addClip(e, t);
        r(e);
      });
    });
  };
  _ctor.prototype.cleanAmClip = function () {
    if (this.myAm) {
      var e = this.myAm.getClips();
      for (var t = e.length - 1; t >= 0; t--) {
        this.myAm.removeClip(e[t], true);
      }
    }
  };
  _ctor.prototype.playAction = function (e, t) {
    if (this.myAm) {
      this.myAm.play(e);
    } else {
      this.mySkeleton && this.setAnimation(e, t);
    }
  };
  _ctor.prototype.getHurt = function (e) {
    var t;
    undefined === e && (e = 1);
    this._tempHurtData || (this._tempHurtData = $2PropertyVo.Hurt.Pool.pop());
    var o = this.property.cut.atk * e;
    (null === (t = this.buffMgr) || undefined === t ? undefined : t.isDisarm) && (o = 0);
    return this._tempHurtData.set({
      baseVal: o,
      critValRate: this.property.cut.CritNum,
      critRate: this.property.cut.crit,
      owner: this,
      bulletVo: null,
      hurCd: 0
    });
  };
  Object.defineProperty(_ctor.prototype, "horDir", {
    get: function () {
      return this._horDir;
    },
    set: function (e) {
      this._horDir = e;
      this.node && this.roleNode && (this.roleNode.scaleX = this._horDir * Math.abs(this.roleNode.scaleX) * this.roleDir);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.addBuff = function (e, t) {
    undefined === t && (t = 1);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    return this.buffMgr.add(e, t);
  };
  _ctor.prototype.addBuffByData = function (e, t) {
    undefined === t && (t = 1);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    return this.buffMgr.addByData(e, t);
  };
  _ctor.prototype.addSkill = function (e, t, o) {
    undefined === t && (t = true);
    undefined === o && (o = false);
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    return this.skillMgr.add(e, t, o);
  };
  _ctor.prototype.addSkillByData = function (e, t, o) {
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    return this.skillMgr.addByData(e, t, o);
  };
  _ctor.prototype.unuse = function () {
    var t;
    var o;
    this._tempHurtData && $2PropertyVo.Hurt.Pool.push(this._tempHurtData);
    this._tempHurtData = null;
    this.isActive = false;
    null === (t = this.skillMgr) || undefined === t || t.destroy();
    null === (o = this.buffMgr) || undefined === o || o.destroy();
    this.removeEffectBox();
    this.changeListener(false);
    e.prototype.unuse.call(this);
  };
  _ctor.prototype.getShowTier = function (e) {
    if (1 == e) {
      return this.botEffectBox;
    } else {
      return this.topEffectBox;
    }
  };
  _ctor.prototype.cleanEvent = function () {
    e.prototype.cleanEvent.call(this);
    this.parent = null;
    this.skillMgr = null;
    this.buffMgr = null;
    this.property = null;
    this.hurtMgr = null;
  };
  _ctor.prototype.enforceNonPeretration = function () {};
  return cc__decorate([ccp_ccclass], _ctor);
}($2MoveEntity.default);
exports.default = def_OrganismBase;