const fs = require('fs');
const path = require('path');

// 修正后的文件分析函数
function analyzeFileStructureFixed(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 修正后的类定义模式 - 包括 def_ 和 exp_ 前缀
        const defClassMatches = content.match(/var\s+(def_|exp_)(\w+)\s*=\s*function\s*\(/g) || [];
        const exportsMatches = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];
        
        // 提取类名
        const classNames = defClassMatches.map(match => {
            const nameMatch = match.match(/var\s+(?:def_|exp_)(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);
        
        const exportClassNames = exportsMatches.map(match => {
            const nameMatch = match.match(/exports\.(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);
        
        // 检查 ccclass 特征
        const ccclassIndicators = [
            /@ccclass/,
            /__decorate\s*\(\s*\[\s*ccp_ccclass/,
            /cc__decorate\s*\(\s*\[\s*ccp_ccclass/,
            /cc\.Class\s*\(/,
            /cc\.Component/,
            /cc\.Node/,
            /@property/,
            /cc\._decorator/,
            /ccp_ccclass/,
            /ccp_property/,
            /ccp_menu/
        ];
        
        const lifecycleMethods = [
            /onLoad\s*[:=]\s*function/,
            /start\s*[:=]\s*function/,
            /update\s*[:=]\s*function/,
            /lateUpdate\s*[:=]\s*function/,
            /onEnable\s*[:=]\s*function/,
            /onDisable\s*[:=]\s*function/,
            /onDestroy\s*[:=]\s*function/
        ];
        
        // 检查非 ccclass 特征
        const nonCcclassIndicators = [
            /exports\.\w+\s*=\s*function\s*\(/,
            /exports\.\w+\s*=\s*["'\d]/,
            /window\.(wx|tt|qq|qg|ks)/,
            /\$2Request\.default\.post/,
            /API_SECRET|DOMAIN|minSdk/
        ];
        
        let ccclassScore = 0;
        let nonCcclassScore = 0;
        
        // 计算分数
        ccclassIndicators.forEach(pattern => {
            if (pattern.test(content)) {
                ccclassScore += 2;
            }
        });
        
        lifecycleMethods.forEach(pattern => {
            if (pattern.test(content)) {
                ccclassScore += 1;
            }
        });
        
        nonCcclassIndicators.forEach(pattern => {
            if (pattern.test(content)) {
                nonCcclassScore += 1;
            }
        });
        
        const result = {
            filePath: filePath,
            fileName: path.basename(filePath),
            defClassCount: defClassMatches.length,
            exportsCount: exportsMatches.length,
            classNames: classNames,
            exportClassNames: exportClassNames,
            ccclassScore: ccclassScore,
            nonCcclassScore: nonCcclassScore,
            totalDefinitions: defClassMatches.length + exportsMatches.length,
            isSingleClass: (defClassMatches.length + exportsMatches.length) === 1,
            isProbablyCcclass: ccclassScore > 0 && ccclassScore >= nonCcclassScore,
            isDefinitelyNotCcclass: nonCcclassScore > ccclassScore && nonCcclassScore > 0
        };
        
        return result;
    } catch (error) {
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            error: error.message,
            isSingleClass: false,
            isProbablyCcclass: false
        };
    }
}

// 重新分析 scripts_5 文件夹
function reanalyzeScripts5() {
    const scriptsDir = './scripts_5';
    
    if (!fs.existsSync(scriptsDir)) {
        console.log('scripts_5 文件夹不存在');
        return;
    }
    
    const files = fs.readdirSync(scriptsDir);
    const jsFiles = files.filter(file => file.endsWith('.js')).map(file => path.join(scriptsDir, file));
    
    console.log(`重新分析 scripts_5 文件夹中的 ${jsFiles.length} 个文件...\n`);
    
    const singleCcclassFiles = [];
    const singleNonCcclassFiles = [];
    const multiDefinitionFiles = [];
    const noDefinitionFiles = [];
    
    jsFiles.forEach(filePath => {
        const analysis = analyzeFileStructureFixed(filePath);
        
        if (analysis.error) {
            console.log(`错误: ${analysis.fileName} - ${analysis.error}`);
            return;
        }
        
        const className = analysis.classNames[0] || analysis.exportClassNames[0] || '未知';
        
        if (analysis.isSingleClass) {
            if (analysis.isProbablyCcclass) {
                singleCcclassFiles.push(analysis);
                console.log(`✓ 单ccclass: ${analysis.fileName} - ${className} (分数: ${analysis.ccclassScore})`);
            } else {
                singleNonCcclassFiles.push(analysis);
                console.log(`- 单非ccclass: ${analysis.fileName} - ${className} (非ccclass分数: ${analysis.nonCcclassScore})`);
            }
        } else if (analysis.totalDefinitions > 1) {
            multiDefinitionFiles.push(analysis);
            console.log(`✗ 多定义: ${analysis.fileName} - 定义数:${analysis.totalDefinitions}`);
        } else {
            noDefinitionFiles.push(analysis);
            console.log(`? 无定义: ${analysis.fileName}`);
        }
    });
    
    console.log(`\n=== 重新分析结果 ===`);
    console.log(`单 ccclass 文件: ${singleCcclassFiles.length} 个`);
    console.log(`单非 ccclass 文件: ${singleNonCcclassFiles.length} 个`);
    console.log(`多定义文件: ${multiDefinitionFiles.length} 个`);
    console.log(`无定义文件: ${noDefinitionFiles.length} 个`);
    
    if (singleCcclassFiles.length > 0) {
        console.log(`\n=== 单 ccclass 文件列表 ===`);
        singleCcclassFiles.forEach(file => {
            const className = file.classNames[0] || file.exportClassNames[0] || '未知';
            console.log(`${file.fileName} - ${className} (分数: ${file.ccclassScore})`);
        });
    }
    
    // 保存结果
    const result = {
        singleCcclassFiles: singleCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.classNames[0] || f.exportClassNames[0] || '未知',
            ccclassScore: f.ccclassScore
        })),
        singleNonCcclassFiles: singleNonCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.classNames[0] || f.exportClassNames[0] || '未知',
            nonCcclassScore: f.nonCcclassScore
        })),
        multiDefinitionFiles: multiDefinitionFiles.map(f => ({
            fileName: f.fileName,
            totalDefinitions: f.totalDefinitions
        })),
        noDefinitionFiles: noDefinitionFiles.map(f => ({
            fileName: f.fileName
        }))
    };
    
    fs.writeFileSync('scripts5_fixed_analysis.json', JSON.stringify(result, null, 2));
    console.log(`\n修正后的分析结果已保存到: scripts5_fixed_analysis.json`);
}

reanalyzeScripts5();
