const fs = require('fs');
const path = require('path');

// 读取修正后的分析结果
function loadFixedAnalysis() {
    try {
        const data = fs.readFileSync('scripts5_fixed_analysis.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('无法读取修正后的分析结果文件:', error.message);
        return null;
    }
}

// 复制文件
function copyFile(sourcePath, targetPath) {
    try {
        fs.copyFileSync(sourcePath, targetPath);
        return true;
    } catch (error) {
        console.error(`复制文件失败: ${sourcePath} -> ${targetPath}`, error.message);
        return false;
    }
}

// 检查文件是否已存在
function fileExists(filePath) {
    return fs.existsSync(filePath);
}

// 主函数
function main() {
    const analysisResult = loadFixedAnalysis();
    if (!analysisResult) {
        return;
    }
    
    const sourceDir = './scripts_5';
    const targetDir = './scripts_ccs';
    
    // 确保目标文件夹存在
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        console.log(`创建目标文件夹: ${targetDir}`);
    }
    
    const singleCcclassFiles = analysisResult.singleCcclassFiles;
    
    console.log(`\n发现 ${singleCcclassFiles.length} 个单 ccclass 文件...\n`);
    
    let newFiles = 0;
    let existingFiles = 0;
    let successCount = 0;
    let failCount = 0;
    
    singleCcclassFiles.forEach((fileInfo, index) => {
        const fileName = fileInfo.fileName;
        const sourcePath = path.join(sourceDir, fileName);
        const targetPath = path.join(targetDir, fileName);
        
        console.log(`[${index + 1}/${singleCcclassFiles.length}] 处理: ${fileName} (${fileInfo.className}, 分数: ${fileInfo.ccclassScore})`);
        
        if (!fs.existsSync(sourcePath)) {
            console.log(`  ✗ 源文件不存在: ${sourcePath}`);
            failCount++;
            return;
        }
        
        if (fileExists(targetPath)) {
            existingFiles++;
            console.log(`  - 文件已存在，跳过`);
            return;
        }
        
        if (copyFile(sourcePath, targetPath)) {
            newFiles++;
            successCount++;
            console.log(`  ✓ 成功复制新文件`);
        } else {
            failCount++;
            console.log(`  ✗ 复制失败`);
        }
    });
    
    console.log(`\n=== 移动完成 ===`);
    console.log(`新复制文件: ${newFiles} 个`);
    console.log(`已存在文件: ${existingFiles} 个`);
    console.log(`复制失败: ${failCount} 个`);
    console.log(`总成功: ${successCount} 个`);
    
    // 显示当前 scripts_ccs 文件夹状态
    if (fs.existsSync(targetDir)) {
        const allFiles = fs.readdirSync(targetDir).filter(file => file.endsWith('.js'));
        console.log(`\nscripts_ccs 文件夹现在包含 ${allFiles.length} 个文件`);
    }
    
    // 按分数排序显示新添加的高分文件
    const newHighScoreFiles = singleCcclassFiles
        .filter(f => {
            const targetPath = path.join(targetDir, f.fileName);
            return fileExists(targetPath) && f.ccclassScore >= 10;
        })
        .sort((a, b) => b.ccclassScore - a.ccclassScore);
    
    if (newHighScoreFiles.length > 0) {
        console.log(`\n=== 高分 ccclass 文件 (分数 >= 10) ===`);
        newHighScoreFiles.slice(0, 20).forEach(file => {
            console.log(`${file.fileName} - ${file.className} (分数: ${file.ccclassScore})`);
        });
        if (newHighScoreFiles.length > 20) {
            console.log(`... 还有 ${newHighScoreFiles.length - 20} 个高分文件`);
        }
    }
    
    // 生成移动报告
    const report = {
        timestamp: new Date().toISOString(),
        sourceDir: sourceDir,
        targetDir: targetDir,
        totalCcclassFiles: singleCcclassFiles.length,
        newFiles: newFiles,
        existingFiles: existingFiles,
        successCount: successCount,
        failCount: failCount,
        highScoreFiles: newHighScoreFiles.length
    };
    
    fs.writeFileSync('new_ccclass_move_report.json', JSON.stringify(report, null, 2));
    console.log(`\n移动报告已保存到: new_ccclass_move_report.json`);
}

main();
