var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_Pop_ShopBoxInfo = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.normalcontent = null;
    t.highcontent = null;
    t.curlv = 1;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    var e;
    var t;
    var o;
    var i;
    this.curlv = this.mode.fightinfopack.getVal("boxlv");
    this.curlv > $2Cfg.Cfg.BoxLevelExp.getArray().length && (this.curlv = $2Cfg.Cfg.BoxLevelExp.getArray().length);
    this.labelArr[0].string = "宝箱奖励";
    this.nodeArr[0].active = !(null === (t = null === (e = this._openArgs) || undefined === e ? undefined : e.param) || undefined === t ? undefined : t.content);
    if (null === (i = null === (o = this._openArgs) || undefined === o ? undefined : o.param) || undefined === i ? undefined : i.content) {
      var n = this._openArgs.param.content;
      if (this.curlv >= $2Cfg.Cfg.BoxLevelExp.getArray().length) {
        this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> MAX", n.from);
      } else {
        this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> LV.%d", n.from, this.curlv + 1);
      }
    }
    this.pageChange(null, 0);
  };
  _ctor.prototype.pageChange = function (e, t) {
    var o = this;
    var i = this.curlv + Number(t);
    var n = $2Cfg.Cfg.BagShopItem.find({
      type: $2CurrencyConfigCfg.CurrencyConfigDefine.Box,
      id: 1e4 + i
    });
    var r = $2Cfg.Cfg.BagShopItem.find({
      type: $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box,
      id: 10100 + i
    });
    if (!(!n || !r || i > $2Cfg.Cfg.BoxLevelExp.getArray().length)) {
      this.curlv += Number(t);
      this.labelArr[1].string = "LV." + this.curlv;
      this.normalcontent.children.forEach(function (e) {
        return e.active = false;
      });
      this.highcontent.children.forEach(function (e) {
        return e.active = false;
      });
      n.boxReward.forEach(function (e, t) {
        var i = e[1];
        var n = e[0];
        e[2];
        var r = o.normalcontent.children[t] || cc.instantiate(o.normalcontent.children[0]);
        r.setAttribute({
          parent: o.normalcontent
        });
        r.active = true;
        var c;
        var l = $2Cfg.Cfg.CurrencyConfig.find({
          id: n
        });
        c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
        $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
        r.getChildByName("num").getComponent(cc.Label).string = i;
      });
      r.boxReward.forEach(function (e, t) {
        var i = e[1];
        var n = e[0];
        e[2];
        var r = o.highcontent.children[t] || cc.instantiate(o.highcontent.children[0]);
        r.setAttribute({
          parent: o.highcontent
        });
        r.active = true;
        var c;
        var l = $2Cfg.Cfg.CurrencyConfig.find({
          id: n
        });
        c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
        $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
        r.getChildByName("num").getComponent(cc.Label).string = i;
      });
    }
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "normalcontent", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "highcontent", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_ShopBoxInfo"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_ShopBoxInfo;