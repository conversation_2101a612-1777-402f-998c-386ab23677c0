var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TaskSaveType = undefined;
var r;
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Time = require("Time");
var $2GameUtil = require("GameUtil");
var $2RBadgeModel = require("RBadgeModel");
(function (e) {
  e.SaveProgressId = "SaveProgressId";
  e.TaskProgressData = "TaskProgressData";
  e.SaveGetTaskId = "SaveGetTaskId";
  e.GameTaskAchieve = "GameTaskAchieve";
  e.GameTaskDay = "GameTaskDay";
  e.GameTaskWeek = "GameTaskWeek";
})(r = exports.TaskSaveType || (exports.TaskSaveType = {}));
var def_TaskModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.commonDayTaskData = [];
    o.commonWeekTaskData = [];
    o.commonProgressDayTaskData = [];
    o.commonProgressWeekTaskData = [];
    o.achieveTaskData = new Map();
    o.achieveTaskList = [];
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initTaskData = function () {
    var e = this;
    this.commonDayTaskData = [];
    var t = [];
    var o = $2Cfg.Cfg.Task.getAll();
    for (var i in o) {
      t[o[i].beyongd - 1] || (t[o[i].beyongd - 1] = []);
      t[o[i].beyongd - 1].push(o[i]);
    }
    t[0] && t[0].forEach(function (t) {
      var o = $2GameUtil.GameUtil.deepCopy(t);
      o.status = e.getGetTaskId(t.id, r.SaveGetTaskId) ? 0 : e.getTaskCurCount(t.type, r.GameTaskDay) >= t.value[0] ? 2 : 1;
      e.commonDayTaskData.push(o);
    });
    this.updateTaskDayRedpoint();
    t[1] && t[1].forEach(function (t) {
      var o = $2GameUtil.GameUtil.deepCopy(t);
      o.status = e.getGetTaskId(t.id, r.SaveGetTaskId) ? 0 : e.getTaskCurCount(t.type, r.GameTaskWeek) >= t.value[0] ? 2 : 1;
      e.commonWeekTaskData.push(o);
    });
    this.updateTaskWeekRedpoint();
    this.achieveTaskList = [];
    this.achieveTaskData.clear();
    var n = [];
    t[2] && t[2].forEach(function (t) {
      n[t.type] || (n[t.type] = []);
      n[t.type].push(t);
      e.achieveTaskData.set(t.type, n[t.type]);
    });
    this.achieveTaskData.forEach(function (t) {
      for (var o = 0; o < t.length; o++) {
        t[o].status = e.getGetTaskId(t[o].id, r.SaveGetTaskId) ? 0 : e.getTaskCurCount(t[o].type, r.GameTaskAchieve) >= t[o].value[0] ? 2 : 1;
        t[o].pre || 0 == t[o].status || e.achieveTaskList.push(t[o]);
      }
      var i;
      var n = t.findIndex(function (e) {
        return 0 != e.status;
      });
      i = -1 != n ? n : t.length - 1;
      e.achieveTaskList.find(function (e) {
        return e.id == t[i].id;
      }) || e.achieveTaskList.push(t[i]);
    });
    this.updateTaskAchieveRedpoint();
  };
  _ctor.prototype.updateTaskDayRedpoint = function () {
    var e = 0;
    for (var t = 0; t < this.commonDayTaskData.length; t++) {
      if (2 == this.commonDayTaskData[t].status) {
        e = 1;
        break;
      }
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.DayTaskItem, e);
  };
  _ctor.prototype.updateTaskWeekRedpoint = function () {
    var e = 0;
    for (var t = 0; t < this.commonWeekTaskData.length; t++) {
      if (2 == this.commonWeekTaskData[t].status) {
        e = 1;
        break;
      }
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.WeekTaskItem, e);
  };
  _ctor.prototype.updateTaskAchieveRedpoint = function () {
    var e = 0;
    for (var t = 0; t < this.achieveTaskList.length; t++) {
      if (2 == this.achieveTaskList[t].status) {
        e = 1;
        break;
      }
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.TaskAchieve, e);
  };
  _ctor.prototype.insertTask = function (e, t) {
    var o = this;
    this.setSaveTaskData(e, t, r.GameTaskDay);
    this.setSaveTaskData(e, t, r.GameTaskWeek);
    this.setSaveTaskData(e, t, r.GameTaskAchieve);
    this.commonDayTaskData && this.commonDayTaskData.forEach(function (e) {
      e.status = o.getGetTaskId(e.id, r.SaveGetTaskId) ? 0 : o.getTaskCurCount(e.type, r.GameTaskDay) >= e.value[0] ? 2 : 1;
    });
    this.updateTaskDayRedpoint();
    this.commonWeekTaskData && this.commonWeekTaskData.forEach(function (e) {
      e.status = o.getGetTaskId(e.id, r.SaveGetTaskId) ? 0 : o.getTaskCurCount(e.type, r.GameTaskWeek) >= e.value[0] ? 2 : 1;
    });
    this.updateTaskWeekRedpoint();
    this.achieveTaskList && this.achieveTaskList.forEach(function (e) {
      e.status = o.getGetTaskId(e.id, r.SaveGetTaskId) ? 0 : o.getTaskCurCount(e.type, r.GameTaskAchieve) >= e.value[0] ? 2 : 1;
    });
    if (this.achieveTaskData) {
      var i = this.achieveTaskData.get(e);
      i && i.forEach(function (e) {
        e.status = o.getGetTaskId(e.id, r.SaveGetTaskId) ? 0 : o.getTaskCurCount(e.type, r.GameTaskAchieve) >= e.value[0] ? 2 : 1;
      });
    }
    this.updateTaskAchieveRedpoint();
  };
  _ctor.prototype.setSaveTaskId = function (e, t) {
    var o = cc.sys.localStorage.getItem(t, []);
    var i = [];
    o && "" != o && (i = (i = JSON.parse(o)) || []);
    i.push(e);
    var n = JSON.stringify(i);
    cc.sys.localStorage.setItem(t, n);
  };
  _ctor.prototype.getGetTaskId = function (e, t) {
    var o = cc.sys.localStorage.getItem(t, []);
    var i = [];
    o && "" != o && (i = (i = JSON.parse(o)) || []);
    return i.includes(e);
  };
  _ctor.prototype.getTaskProgressReward = function () {
    var e = $2Cfg.Cfg.ProcessRewards.getAll();
    for (var t in e) {
      if (1 == e[t].beyongd) {
        this.commonProgressDayTaskData.push(e[t]);
      } else {
        2 == e[t].beyongd && this.commonProgressWeekTaskData.push(e[t]);
      }
    }
    this.commonProgressDayTaskData.sort(function (e, t) {
      return e.proId - t.proId;
    });
    this.updateTaskProgressData(0);
    this.commonProgressWeekTaskData.sort(function (e, t) {
      return e.proId - t.proId;
    });
    this.updateTaskProgressData(1);
  };
  _ctor.prototype.updateTaskProgressData = function (e) {
    var t = 0 == e ? this.commonProgressDayTaskData : this.commonProgressWeekTaskData;
    var o = this.getTaskCurCount(e + 1, r.TaskProgressData);
    var i = 0;
    for (var n = 0; n < 5; n++) {
      if (!this.getGetTaskId(t[n].proId, r.SaveProgressId) && o >= t[n].needNum) {
        i = 1;
        break;
      }
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, 0 == e ? $2RBadgeModel.RBadge.Key.DayTaskBox : $2RBadgeModel.RBadge.Key.WeekTaskBox, i);
  };
  _ctor.prototype.setSaveTaskData = function (e, t, o) {
    var i = cc.sys.localStorage.getItem(o);
    if (i && "" != i) {
      var n = JSON.parse(i);
      var r = new Map(n);
      if (r.get(e)) {
        if (10 == e) {
          r.set(e, t);
        } else {
          r.set(e, r.get(e) + t);
        }
      } else {
        r.set(e, t);
      }
      var a = JSON.stringify(Array.from(r));
      cc.sys.localStorage.setItem(o, a);
    } else {
      var s = new Map();
      s.set(1, 1);
      s.set(e, t);
      a = JSON.stringify(Array.from(s));
      cc.sys.localStorage.setItem(o, a);
    }
  };
  _ctor.prototype.getTaskCurCount = function (e, t) {
    var o = cc.sys.localStorage.getItem(t);
    if (o && "" != o) {
      var i = JSON.parse(o);
      var n = new Map(i);
      if (n.get(e)) {
        return n.get(e);
      }
    }
    return 0;
  };
  _ctor.prototype.updateAchieveTaskData = function (e, t) {
    var o = this;
    var i = this.achieveTaskList.find(function (t) {
      return t.id == e;
    });
    i && (i.status = 0);
    var n = this.achieveTaskData.get(t);
    if (n) {
      var s = n.find(function (t) {
        return t.id == e;
      });
      s && (s.status = 0);
      var c;
      var u = n.findIndex(function (e) {
        return 0 != e.status;
      });
      c = -1 != u ? u : n.length - 1;
      var p = [];
      this.achieveTaskList.forEach(function (e) {
        if (!(e.type == n[c].type && 0 == e.status)) {
          e.status = o.getGetTaskId(e.id, r.SaveGetTaskId) ? 0 : o.getTaskCurCount(e.type, r.GameTaskAchieve) >= e.value[0] ? 2 : 1;
          p.push(e);
        }
      });
      p.push(n[c]);
      this.achieveTaskList = p;
      $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateAchieveDate);
    }
  };
  _ctor.prototype.clearTaskData = function (e) {
    var t = cc.sys.localStorage.getItem(r.SaveProgressId, []);
    var o = [];
    t && "" != t && (o = (o = JSON.parse(t)) || []);
    var i = new Set();
    var n = $2Cfg.Cfg.ProcessRewards.getAll();
    for (var a in n) {
      n[a].beyongd == e && i.add(n[a].proId);
    }
    o.length > 0 && (o = o.filter(function (e) {
      return !i.has(e);
    }));
    var c = JSON.stringify(o);
    cc.sys.localStorage.setItem(r.SaveProgressId, c);
    var l = cc.sys.localStorage.getItem(r.TaskProgressData);
    if (l && "" != l) {
      var u = JSON.parse(l);
      var p = new Map(u);
      p.get(e) && p.set(e, 0);
      var f = JSON.stringify(Array.from(p));
      cc.sys.localStorage.setItem(r.TaskProgressData, f);
    }
    var h = cc.sys.localStorage.getItem(r.SaveGetTaskId, []);
    var d = [];
    h && "" != h && (d = (d = JSON.parse(h)) || []);
    var g = new Set();
    var y = $2Cfg.Cfg.Task.getAll();
    for (var a in y) {
      y[a].beyongd == e && g.add(y[a].id);
    }
    d.length > 0 && (d = d.filter(function (e) {
      return !g.has(e);
    }));
    var m = JSON.stringify(d);
    cc.sys.localStorage.setItem(r.SaveGetTaskId, m);
    cc.sys.localStorage.setItem(1 == e ? r.GameTaskDay : r.GameTaskWeek, "[]");
  };
  _ctor.prototype.getNextDay = function (e) {
    undefined === e && (e = 1);
    var t = new Date($2Time.Time.serverTimeMs);
    t.setHours(0, 0, 0, 0);
    return t.getTime() + 864e5 * e;
  };
  _ctor.prototype.setMultipleSort = function (e, t) {
    e.sort(function (e, o) {
      for (var i in t) {
        if (t.hasOwnProperty(i)) {
          var n = e[i];
          var r = o[i];
          if (n !== r) {
            return t[i] * (n - r);
          }
        }
      }
      return 0;
    });
  };
  _ctor.prototype.calculateProgress = function (e, t) {
    var o = t.findIndex(function (o, i) {
      if (i === t.length - 1) {
        return e >= o;
      } else {
        return e < t[i + 1];
      }
    });
    if (-1 === o) {
      return 0;
    }
    var i = t[o];
    var n = o === t.length - 1 ? e : t[o + 1];
    return (o + (o === t.length - 1 ? 1 : (e - i) / (n - i))) / (t.length - 1);
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_TaskModel;