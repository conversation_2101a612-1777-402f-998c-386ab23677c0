var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeChainsModel = require("ModeChainsModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M33_Pop_Revive = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    this.game.clearAllBullet();
    this.labelArr[0].string = "(" + (this.game.mainRole.reliveNum + 1) + "/" + $2Manager.Manager.vo.switchVo.dragonRevive + ")";
  };
  _ctor.prototype.onClickRelive = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ReliveSuccess);
    this.close();
  };
  _ctor.prototype.onClickGiveUp = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End);
    this.close();
  };
  _ctor.prototype.onClickFrame = function () {
    this.onClickGiveUp();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/M33_Pop_Revive"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M33_Pop_Revive;