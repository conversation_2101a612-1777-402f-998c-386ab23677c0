var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MBPack = undefined;
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2GameSettingCfg = require("GameSettingCfg");
var $2GameatrCfg = require("GameatrCfg");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2GameUtil = require("GameUtil");
var $2KnapsackVo = require("KnapsackVo");
var $2RecordVo = require("RecordVo");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2BaseEntity = require("BaseEntity");
var $2MonsterTidalBoss = require("MonsterTidalBoss");
var $2CompManager = require("CompManager");
var $2NodePool = require("NodePool");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20Prop = require("M20Prop");
var $2M20Prop_Equip = require("M20Prop_Equip");
var $2BackHeroProp = require("BackHeroProp");
var $2BackpackHeroHome = require("BackpackHeroHome");
(function (e) {
  var t;
  var o;
  var i;
  var D;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.BATTLE = 1] = "BATTLE";
    e[e.ONTICK = 2] = "ONTICK";
    e[e.SELECTEQUIP = 3] = "SELECTEQUIP";
    e[e.END = 4] = "END";
  })(o = e.RoundStatus || (e.RoundStatus = {}));
  (function (e) {
    e[e.MergeEquipment = 1] = "MergeEquipment";
    e[e.Block = 2] = "Block";
    e[e.Gem = 3] = "Gem";
  })(e.PropType || (e.PropType = {}));
  (function (e) {
    e[e.Default = 1] = "Default";
    e[e.Challenge = 2] = "Challenge";
    e[e.ChallengeCoin = 3] = "ChallengeCoin";
  })(i = e.PassType || (e.PassType = {}));
  (function (e) {
    e[e.Upgrade = 0] = "Upgrade";
    e[e.Mosaic = 1] = "Mosaic";
    e[e.GreedyGemstone = 4e3] = "GreedyGemstone";
    e[e.MagicallyChange = 4001] = "MagicallyChange";
    e[e.DestinyGem = 4002] = "DestinyGem";
    e[e.CopyGem = 4003] = "CopyGem";
    e[e.Sacrifice = 4004] = "Sacrifice";
    e[e.CopyEquip = 4005] = "CopyEquip";
    e[e.MinEquip = 4010] = "MinEquip";
    e[e.GeGeJi = 4012] = "GeGeJi";
    e[e.BackPack = 4013] = "BackPack";
    e[e.MOONCAKE = 4015] = "MOONCAKE";
    e[e.SWALLOW = 5e3] = "SWALLOW";
  })(e.ReactionType || (e.ReactionType = {}));
  e.equipWeapon = [1e3, 1002, 1003, 1004, 1005, 1007, 1008, 1010, 1011, 1012, 1013, 1014, 1015, 1017, 1018, 1019];
  e.equipArmor = [1001, 1006, 1009, 1016];
  (function (e) {
    e[e.Start = 900] = "Start";
    e[e.Start2 = 901] = "Start2";
  })(D = e.BlockType || (e.BlockType = {}));
  e.BlockLimt = {
    1: ["11111", "11111", "11111", "11111", "11111", "11111", "11111"],
    2: ["00100", "01110", "11111", "11111", "11111", "01110", "00100"],
    3: ["11111", "01110", "01110", "11111", "01110", "01110", "11111"],
    4: ["01111", "11110", "01111", "11110", "01111", "11110", "01111"],
    10: ["11111", "11111", "11111", "11111", "11111", "11111", "11111", "11111", "11111"]
  };
  e.BlockPos = ((t = {
    1: [cc.v2(0, 0)],
    4: [cc.v2(0, 0), cc.v2(1, 0), cc.v2(1, 1), cc.v2(0, 1)],
    21: [cc.v2(0, 0), cc.v2(1, 0)],
    22: [cc.v2(0, 0), cc.v2(0, 1)],
    31: [cc.v2(0, 0), cc.v2(1, 0), cc.v2(2, 0)],
    32: [cc.v2(0, 0), cc.v2(0, 1), cc.v2(0, 2)],
    33: [cc.v2(1, 1), cc.v2(0, 1), cc.v2(1, 0)],
    34: [cc.v2(1, 0), cc.v2(0, 0), cc.v2(1, 1)],
    35: [cc.v2(0, 0), cc.v2(1, 0), cc.v2(0, 1)],
    36: [cc.v2(0, 1), cc.v2(0, 0), cc.v2(1, 1)],
    41: [cc.v2(0, 0), cc.v2(1, 0), cc.v2(2, 0), cc.v2(3, 0)],
    42: [cc.v2(0, 0), cc.v2(0, 1), cc.v2(0, 2), cc.v2(0, 3)],
    50: [cc.v2(0, 2), cc.v2(1, 2), cc.v2(2, 2), cc.v2(1, 1), cc.v2(1, 0)]
  })[D.Start] = [cc.v2(-1, 1), cc.v2(0, 1), cc.v2(1, 1), cc.v2(-1, 0), cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, -1), cc.v2(0, -1), cc.v2(1, -1)], t[D.Start2] = [cc.v2(-1, 1), cc.v2(0, 1), cc.v2(1, 1), cc.v2(-1, 0), cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, -1), cc.v2(0, -1), cc.v2(1, -1), cc.v2(-1, -2), cc.v2(0, -2), cc.v2(1, -2)], t[3060] = [cc.v2(-1, 2), cc.v2(0, 2), cc.v2(-1, 1), cc.v2(0, 1), cc.v2(1, 1), cc.v2(-1, 0), cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, -1), cc.v2(0, -1), cc.v2(1, -1)], t[3061] = [cc.v2(-1, 2), cc.v2(0, 2), cc.v2(-1, 1), cc.v2(0, 1), cc.v2(1, 1), cc.v2(-1, 0), cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, -1), cc.v2(0, -1), cc.v2(1, -1), cc.v2(-1, -2), cc.v2(0, -2), cc.v2(1, -2)], t[4001] = [cc.v2(0, 2), cc.v2(-1, 1), cc.v2(0, 1), cc.v2(1, 1), cc.v2(-1, 0), cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, -1), cc.v2(0, -1), cc.v2(1, -1), cc.v2(0, -2), cc.v2(-2, 0), cc.v2(2, 0)], t);
  e.BlockSize = 110;
  var T = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t.FightID = $2Time.Time.serverTimeMs;
      t.Buff = [];
      t.EquipList = [];
      t.SpareList = [];
      t.StoreList = [];
      t.Block2 = [];
      t.OffsetBolck = cc.v2();
      t.level = 0;
      t.batch = 0;
      t.knapsackList = [];
      t.BuffADReset = 10;
      t.BuffADGetAll = 3;
      t.freeTime = 1;
      t.isUnlockStore = false;
      t.Lv3EquipAppear = 0;
      t.unlockSpeed = [1];
      t.cutSelectSpeed = 1;
      t.isGemTips = [];
      t.gemAppearBegin = [];
      t.gemWeightList = {};
      t.gemGetNum = [[4013, 1], [4012, 1]];
      t.killNum = 0;
      t.countdownTime = 0;
      t.adRefreshEquip = 9999;
      t.ADNum = 0;
      return t;
    }
    cc__extends(t, e);
    return t;
  }($2RecordVo.RecordVo.Data);
  e.RecordData = T;
  var A = function (t) {
    function u(e) {
      var o;
      var n;
      var r;
      var a = t.call(this, e) || this;
      a.cameraZoomRatio = 1;
      a.recordVo = new $2RecordVo.RecordVo.Mgr("MBackpackHero", function () {
        return new T();
      });
      a.param = {};
      a.scenceSize = [-375, 375, -667, 667];
      a.curBackHeroProp = [];
      a.lastReadData = a.recordVo.ReadData();
      a.passParam = (null === (o = a.lastReadData) || undefined === o ? undefined : o.passParam) || e;
      a.passType = (null === (n = a.lastReadData) || undefined === n ? undefined : n.passType) || (null == e ? undefined : e.PassType) || i.Default;
      a.level = (null === (r = a.lastReadData) || undefined === r ? undefined : r.level) || a.passParam.Lv || a.mode.PlayingLv;
      return a;
    }
    cc__extends(u, t);
    Object.defineProperty(u.prototype, "rVo", {
      get: function () {
        return this.recordVo.vo;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(u.prototype, "batchNum", {
      get: function () {
        return this.bronMonsterMgr.batchNum;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(u.prototype, "isChallenge", {
      get: function () {
        return [e.PassType.Challenge, e.PassType.ChallengeCoin].includes(this.passType);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(u.prototype, "cgScoreAdd", {
      get: function () {
        var e = this;
        var t = 0;
        this.passParam.Buff.forEach(function (o) {
          var i = e.mode.curActivity.buff.find(function (e) {
            return e[0] == o;
          });
          i && (t += i[2]);
          var n = e.mode.curActivity.debuff.find(function (e) {
            return e[0] == o;
          });
          n && (t += n[2]);
        });
        return 1 + t;
      },
      enumerable: false,
      configurable: true
    });
    u.prototype.changeListener = function (e) {
      t.prototype.changeListener.call(this, e);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_ClickBackHome, this.onFight_ClickBackHome, this);
      $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Fight_GetFightID, this.getFightID, this);
    };
    u.prototype.getFightID = function () {
      return this.rVo.FightID || 0;
    };
    u.prototype.loadMap = function (e, t) {
      var o;
      var n = this;
      this.gameNode = e;
      this._entityNode = e.getChildByName("entityNode");
      this._bulletNode = e.getChildByName("bulletNode");
      this._mapNode = e.getChildByName("mapNode");
      this._botEffectNode = e.getChildByName("botEffectNode");
      this._topEffectNode = e.getChildByName("topEffectNode");
      this.LifeBarUI = e.getORaddChildByName("LifeBarUI");
      this.topUINode = e.getORaddChildByName("topUINode");
      this.behitUI = e.getORaddChildByName("behitUI");
      this.atkRangeImg = e.getChildByName("attackr");
      this._finishCall = t;
      $2Manager.Manager.loader.loadPrefab("entity/fight/ModeBackpackHero/map").then(function (e) {
        e.parent = n._mapNode;
        e.zIndex = -1;
        var t = $2Cfg.Cfg.BagModeLv.get(n.level);
        var o = null == t ? undefined : t.sceneMap;
        o || (o = "img/map/ground0" + $2GameUtil.GameUtil.random(1, 12));
        $2Manager.Manager.loader.loadSpriteToSprit(o, e.getComponent(cc.Sprite));
      });
      this.knapsackMgr = new $2KnapsackVo.KnapsackVo.Mgr(false, "MBackpackHero");
      this._createBronMonster();
      if (!this.lastReadData) {
        this.isChallenge && this.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, this.mode.curActivity.silverCoin);
        var r = $2Manager.Manager.vo.switchVo.gemAppearBegin.find(function (e) {
          return e[0] == n.level;
        }) || $2Manager.Manager.vo.switchVo.gemAppearBegin.lastVal;
        this.recordVo.SetVal({
          adGrid: $2Manager.Manager.vo.switchVo.adGrid[0],
          Lv3EquipAppear: $2Manager.Manager.vo.switchVo.Lv3EquipAppear[0] ? $2Manager.Manager.vo.switchVo.Lv3EquipAppear[2] : 0,
          gemAppearBegin: [9999, 9999, 9999, r[2], r[3]],
          countdownTime: this.passType == i.ChallengeCoin ? 300 : 99999,
          adRefreshEquip: $2Manager.Manager.vo.switchVo.adRefreshEquip[0] >= 0 ? $2Manager.Manager.vo.switchVo.adRefreshEquip[0] : 9999
        });
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Equip", {
          EquipFightBegin: this.mode.userEquipPack.filter(function (e) {
            return e.isFitOut;
          }).map(function (e) {
            return [e.id, e.num];
          }),
          FightID: $2Notifier.Notifier.call($2CallID.CallID.Fight_GetFightID) || 0
        });
      }
      var u = (null === (o = this.lastReadData) || undefined === o ? undefined : o.role) || this.mode.cutUseRole;
      var p = u.roleId;
      var d = u.lv;
      var m = $2Cfg.Cfg.RoleLv.find({
        roleId: p || 3002,
        lv: d || 1
      });
      this.createHomeBuild(m);
      this.gameCamera.lookPos.set(cc.v2(0, $2GameUtil.GameUtil.getDesignSize.height / 2));
      this.sendEvent("Start");
      this.scenceSize = [-375 / this.gameCamera.cutZoomRatio * .8, 375 / this.gameCamera.cutZoomRatio * .8, 0, 0];
      this.propSkilling = new Set();
    };
    Object.defineProperty(u.prototype, "mode", {
      get: function () {
        return $2ModeBackpackHeroModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(u.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    u.prototype.saveRecordVo = function () {
      var e = this;
      this.timeDelay.cancelBy(this.saveRecordTimer);
      this.saveRecordTimer = this.timeDelay.delay(.5, function () {
        if (e.gameNode.isValid && e.mainRole.isValid) {
          var t = e.recordVo.vo;
          t.Buff.length = 0;
          var i = e.packView;
          var n = e.mainRole;
          t.role = {
            curHp: n.curHp,
            _level: n._level,
            _levelExp: n._levelExp,
            _nextLevelExp: n._nextLevelExp,
            roleId: n.lvCfg.roleId,
            lv: n.lvCfg.lv
          };
          n.buffMgr.bufflist.forEach(function (t) {
            $2Game.ModeCfg.Buff.get(t.cutVo.id) && e.recordVo.vo.Buff.push(t.cutVo.id);
          });
          t.EquipList.length = 0;
          i.propList.forEach(function (e) {
            t.EquipList.push(e.saveData);
          });
          t.Block2.length = 0;
          i.map.get(2).box.forEach(function (e) {
            t.Block2.push({
              pos: e.actualPos
            });
          });
          t.SpareList.length = 0;
          i.SpareBox.children.forEach(function (e) {
            var o = e.getComponent($2M20Prop.default);
            t.SpareList.push(o.saveData);
          });
          t.StoreList.length = 0;
          i.StoreList.forEach(function (e) {
            return t.StoreList.push(e.saveData);
          });
          t.knapsackList.length = 0;
          e.knapsackMgr.forEach(function (e) {
            return t.knapsackList.push(e);
          });
          t.OffsetBolck = i.OffsetBolck;
          t.level = e.bronMonsterMgr.level;
          t.batch = e.bronMonsterMgr.cutStatus == o.BATTLE ? e.bronMonsterMgr.batchNum - 1 : e.bronMonsterMgr.batchNum;
          t.passParam = e.passParam;
          t.passType = e.passType;
          t.ADNum = $2Manager.Manager.AD.data.getor($2Game.Game.Mode.BACKPACKHERO, 0);
          e.recordVo.SaveData();
        }
      }).id;
    };
    u.prototype.readRecordData = function (e) {
      var t = this;
      var o = this.mainRole;
      var i = this.packView;
      e.Buff.forEach(function (e) {
        t.mainRole.addBuff(e);
      });
      this.bronMonsterMgr.level = e.level;
      this.bronMonsterMgr.batchNum = e.batch;
      for (var n in e.role) {
        o[n] && (o[n] = e.role[n]);
      }
      e.knapsackList.forEach(function (e) {
        return t.knapsackMgr.set(e);
      });
      i.OffsetBolck.setVal(e.OffsetBolck.x, e.OffsetBolck.y);
      i.newBoclkItem(e.Block2.length).then(function (t) {
        t.forEach(function (t, o) {
          t.setPosition(cc.v2(e.Block2[o].pos.x, e.Block2[o].pos.y));
          i.map.get(2).box.push(t);
          var n = i.toMapPos(t.position);
          t.set(2, cc.v2(n.x + 1, n.y + 1));
        });
        i.getBlockSize(1);
        i.scheduleOnce(function () {
          i.packState = 1;
          i.resetMap(1);
        }, .11);
        i.resetMap(2);
      });
      e.SpareList.forEach(function (e) {
        var t = $2Cfg.Cfg.EquipMergeLv.get(e.id);
        i.newProp(t, {}, e).then(function (t) {
          i.setInSpareBox(t);
          t.readData(e);
        });
      });
      e.StoreList.forEach(function (e) {
        var t = $2Cfg.Cfg.EquipMergeLv.get(e.id);
        i.newProp(t, {}, e).then(function (t) {
          t.node.setAttribute({
            parent: i.MoveBox,
            position: cc.v2(e.pos.x, e.pos.y)
          });
          i.StoreList.set(t.ID, t);
          t.readData(e);
        });
      });
      i.scheduleOnce(function () {
        e.EquipList.forEach(function (e) {
          var t = $2Cfg.Cfg.EquipMergeLv.get(e.id);
          var o = cc.v2(e.pos.x, e.pos.y);
          i.newProp(t, {
            position: o,
            parent: i.EquipmentBox,
            scale: 0
          }, e).then(function (t) {
            var o = [];
            t.seachPoint.forEach(function (e) {
              i.checkMap(e, 2).forEach(function (e) {
                o.push(e);
              });
            });
            i.setInPack(t, o);
            cc.tween(t.node).to(.2, {
              scale: 1
            }, {
              easing: cc.easing.backOut
            }).start();
            t.readData(e);
          });
        });
      }, .3);
      $2Manager.Manager.AD.data.set($2Game.Game.Mode.BACKPACKHERO, e.ADNum);
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ExpUpdate, this.mainRole.levelExp, 0);
    };
    u.prototype.analysisBlockLimt = function (t) {
      var o = cc.size(t[0].length, t.length);
      var i = [];
      t.forEach(function (t, n) {
        t.split("").forEach(function (t, r) {
          if ("0" != t) {
            var a = cc.v2(r - o.width / 2, n - o.height / 2);
            a.mulSelf(e.BlockSize);
            a.addSelf(cc.v2(e.BlockSize / 2, e.BlockSize / 2));
            i.push(a);
          }
        });
      });
      return i;
    };
    u.prototype.gemGetNum = function (e, t) {
      undefined === t && (t = 0);
      var o = this.rVo.gemGetNum.find(function (t) {
        return t[0] == e;
      });
      if (o) {
        return o[1] -= t, o[1];
      } else {
        return 999;
      }
    };
    u.prototype.onFight_ClickBackHome = function () {
      this.recordVo.CleanData();
    };
    u.prototype.reloadPropToScence = function () {
      var e = this;
      this.mainRole.petList.length = 0;
      var t = this.packView.propList;
      this.removeEquipmen();
      t.forEach(function (t) {
        t instanceof $2M20Prop_Equip.default && e.createProp(t);
      });
      this.mainRole.buffMgr.use(3021, false, function () {
        var t = e.packView.StoreList.getFirst();
        t && t instanceof $2M20Prop_Equip.default && e.createProp(t).then(function (t) {
          e.mainRole.buffMgr.use(3023, false, function (e) {
            t.addBuff(e.cutVo.value[0][0]);
          });
        });
      });
    };
    u.prototype.removeEquipmen = function () {
      for (var e = this.curBackHeroProp.length - 1; e >= 0; e--) {
        this.curBackHeroProp[e].removeEntityToUpdate();
        $2GameUtil.GameUtil.deleteArrItem(this.curBackHeroProp, this.curBackHeroProp[e]);
      }
    };
    u.prototype.createProp = function (e) {
      var t = this;
      return new Promise(function (o) {
        $2NodePool.NodePool.spawn("entity/fight/ModeBackpackHero/BackHeroProp").setNodeAssetFinishCall(function (i) {
          var n = i.getComponent($2BackHeroProp.default);
          i.setAttribute({
            parent: t._entityNode,
            position: t.mainRole.roleNode.position.add(t.mainRole.position)
          });
          n.parent = t.mainRole;
          n.init();
          n.set(e);
          $2CompManager.default.Instance.registerComp(n);
          t.elementMap.set(n.ID, n);
          t.curBackHeroProp.push(n);
          t.uiView.resetEquipList();
          o(n);
        });
      });
    };
    u.prototype._createBronMonster = function () {
      this.bronMonsterMgr = this.gameNode.getORaddComponent(R);
      this.bronMonsterMgr.init();
    };
    u.prototype.createHomeBuild = function (e) {
      var t = this;
      return new Promise(function (o) {
        if (t.mainRole) {
          return o(t.mainRole);
        }
        $2NodePool.NodePool.spawn("entity/fight/ModeBackpackHero/BackpackHeroHome").setNodeAssetFinishCall(function (i) {
          var n = i.getComponent($2BackpackHeroHome.default);
          t.mainRole = n;
          i.setAttribute({
            parent: t._topEffectNode,
            position: cc.v2(0, 0)
          });
          n.init();
          n.setData(e);
          $2CompManager.default.Instance.registerComp(n);
          o(t.mainRole);
          t._finishCall && t._finishCall();
          t.elementMap.set(n.ID, n);
        });
      });
    };
    u.prototype.getTarget = function (e) {
      var t;
      e.target || (e.target = this.mainRole);
      e.pos || (e.pos = e.target.position.clone());
      e.maxNum || (e.maxNum = 10);
      e.radius || (e.radius = 350);
      e.ignoreID || (e.ignoreID = []);
      e.targetCamp || (e.targetCamp = [e.target.atkCamp]);
      e.minRadius || (e.minRadius = 30);
      var o = (null === (t = e.target) || undefined === t ? undefined : t.node.y) || 0;
      if (e.target.atkCamp == $2BaseEntity.CampType.Two) {
        o = this.mainRole.node.y + .9 * this.mainRole.node.height;
      } else if (e.target.atkCamp == $2BaseEntity.CampType.One) {
        return [this.mainRole];
      }
      var i = [];
      for (var n = 0; n < this._MonsterOnScreenList.length; n++) {
        var r = this._MonsterOnScreenList[n];
        if (r.isActive && !e.ignoreID.includes(r.ID) && !r.isInvincible) {
          var a = Math.abs(r.node.y - o);
          if (!(a > e.radius)) {
            r.node.t_sort = a;
            i.push(r);
          }
        }
      }
      $2GameUtil.GameUtil.sort(i, function (e, t) {
        return t.node.t_sort < e.node.t_sort;
      });
      if ((i = i.splice(0, e.maxNum)).length > 0) {
        for (; i.length < e.maxNum;) {
          i.push($2GameUtil.GameUtil.getRandomInArray(i)[0]);
        }
      }
      return i;
    };
    u.prototype.reliveSuccess = function () {
      this.mainRole.relive();
    };
    u.prototype.getCutGameData = function () {
      var e;
      var o;
      return cc__assign(cc__assign({}, t.prototype.getCutGameData.call(this)), {
        PassType: this.passType,
        Block: null === (e = this.packView) || undefined === e ? undefined : e.BlockBox_2.childrenCount,
        EBlock: null === (o = this.packView) || undefined === o ? undefined : o.hasSpace
      });
    };
    u.prototype.onUpdate = function (e) {
      t.prototype.onUpdate.call(this, e);
    };
    return u;
  }($2Game.Game.Mgr);
  e.Mgr = A;
  cc.Vec2.ZERO;
  var R = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._batchNum = 0;
      t.cutBatchDrop = new Map();
      t.batchInterval = [30];
      t.allBatch = 0;
      t.cutStatus = o.NONE;
      t.lastStatus = o.NONE;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        var t = this;
        this._batchNum = e;
        if (0 != e) {
          this.RoundMonster = this.MonsterLv.filter(function (e) {
            return e.round == t._batchNum;
          });
          this._batchSumCount = 0;
          var o = false;
          this.role.buffMgr.use(4060, false, function (e) {
            e.isWeight && (o = true);
          });
          this.RoundMonster.forEach(function (e) {
            t._batchSumCount += e.sumCount;
            o && (e.sumCount *= 2);
          });
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
          this.cutBatchDrop.clear();
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "RoundMonster", {
      get: function () {
        return this._RoundMonster;
      },
      set: function (e) {
        var t = this;
        this._RoundMonster.length = 0;
        var o = $2Manager.Manager.vo.switchVo.lvDiffEasy.filter(function (e) {
          return e[0] == t.level;
        });
        if (0 == o.length) {
          var i = $2Manager.Manager.vo.switchVo.lvDiffEasy.lastVal[0];
          o = $2Manager.Manager.vo.switchVo.lvDiffEasy.filter(function (e) {
            return e[0] == i;
          });
        }
        this.game.rVo.ADNum < $2Manager.Manager.vo.switchVo.lvDiffAd && (o.length = 0);
        e.forEach(function (e) {
          var i = [];
          e.monId.forEach(function (e) {
            return i.push({
              id: e,
              w: 100
            });
          });
          t.role.buffMgr.attrBuffMap.getor($2GameatrCfg.GameatrDefine.monsterAppear, []).forEach(function (e) {
            var t = i.find(function (t) {
              return t.id == e.otherValue[0];
            });
            t && (t.w += e.cutVo.value[0][0]);
          });
          var n = $2GameUtil.GameUtil.weightGetValue(i).id;
          var a = o.find(function (t) {
            return e.round > t[1] && e.round <= t[2];
          });
          if (a) {
            e.hp *= 1 + a[3];
            e.atk *= 1 + a[4];
          }
          var s = $2Cfg.Cfg.Monster.get(n);
          s && t._RoundMonster.push(cc__assign({
            letTime: 0,
            createNum: 0,
            type: s.type,
            monsterID: n
          }, e));
        });
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.init = function () {
      var e = this;
      this.level = this.game.level;
      this._maxLen = Math.max(cc.winSize.width, cc.winSize.height) / 2;
      this.batchNum = 0;
      this.batchInterval = $2Cfg.Cfg.GameSetting.get($2GameSettingCfg.GameSettingDefine.tdtime).name;
      var t = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (t) {
        return t[0] == e.level;
      });
      if (0 == t.length && this.game.passType == i.Default) {
        var o = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (e) {
          return e[0] < 9e3;
        });
        var n = o.lastVal[0];
        t = o.filter(function (e) {
          return e[0] == n;
        });
      }
      var r = $2Cfg.Cfg.BagModeLv.get(this.level);
      this.MonsterLv = JSON.parse(JSON.stringify($2Game.ModeCfg.MonsterLv.filter({
        lv: (null == r ? undefined : r.lvMould) ? $2GameUtil.GameUtil.randomArr(r.lvMould) : this.level
      })));
      this.MonsterLv.forEach(function (e) {
        if (null == r ? undefined : r.lvMould) {
          e.hp *= r.diff[0];
          e.atk *= r.diff[1];
          e.speed *= r.diff[2];
        }
        var o = t.find(function (t) {
          return e.round > t[1] && e.round <= t[2];
        });
        if (o) {
          e.hp *= 1 + o[3];
          e.atk *= 1 + o[4];
        }
      });
      this.allBatch = this.MonsterLv[this.MonsterLv.length - 1].round;
    };
    Object.defineProperty(t.prototype, "survivalMonsterNum", {
      get: function () {
        return this.game.monsterMap.size;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onUpdate = function (e) {
      if (this.game.gameState != $2Game.Game.State.PAUSE && this.cutStatus == o.BATTLE) {
        this.game.rVo.countdownTime > 0 && (this.game.rVo.countdownTime -= e);
        (this._dtTime += e) >= 1 && (this._dtTime = 0);
        if (this._batchSumCount > 0) {
          this.checkAddMonster(e);
        } else if (0 == this._dtTime) {
          if (this.game.passType == i.ChallengeCoin) {
            return this.changeGameStatus(o.ONTICK);
          }
          0 == this.survivalMonsterNum && this.changeGameStatus(o.ONTICK);
        }
      }
    };
    t.prototype.changeGameStatus = function (e) {
      var t = this;
      switch (e) {
        case o.BATTLE:
          this._dtTime = .01;
          this.game.propSkilling.clear();
          this.game.reloadPropToScence();
          this.batchNum++;
          this.game.canSkill = true;
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
          $2Game.Game.setGameSpeed($2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutSelectSpeed) || 1);
          this.game.saveRecordVo();
          break;
        case o.ONTICK:
          this.game.sendEvent("BatchWin");
          if (!this.MonsterLv.find(function (e) {
            return e.round == t._batchNum + 1;
          })) {
            return $2Notifier.Notifier.send($2ListenID.ListenID.Fight_Win);
          }
          if (this.game.passType == i.ChallengeCoin) {
            return void this.batchNum++;
          }
          this.game.clearAllBullet();
          this.game.removeEquipmen();
          break;
        case o.SELECTEQUIP:
          $2Game.Game.setGameSpeed(1);
          this.game.removeEquipmen();
          this.game.packView.show();
          this.game.clearAllBullet();
          this.game.saveRecordVo();
          break;
        case o.END:
      }
      this.lastStatus = this.cutStatus;
      this.cutStatus = e;
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_RoundState);
    };
    Object.defineProperty(t.prototype, "maxLen", {
      get: function () {
        return $2GameUtil.GameUtil.getDesignSize.height / this.game.gameCamera.cutZoomRatio;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "randomL", {
      get: function () {
        return $2Game.Game.random(this.maxLen, this.maxLen + 100);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "randomPos", {
      get: function () {
        return this.mainRole.position.add(cc.v2($2Game.Game.random(this.game.scenceSize[0], this.game.scenceSize[1]), this.randomL));
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.checkAddMonster = function (e) {
      var t = this;
      this.mainRole && !this.mainRole.isDead && (this.isBossRound || this._RoundMonster.forEach(function (o) {
        o.letTime += e;
        if (o.createNum < o.sumCount && o.letTime > o.bronTime) {
          o.letTime = 0;
          o.createNum++;
          o.Count > 200 && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ShowGameTips, 3, "尸潮来袭");
          if (3 == $2Cfg.Cfg.Monster.get(o.monsterID).moveType) {
            t.addMonsterById(o, t.randomPos.sub(cc.v2(0, .4 * t.randomL)));
          } else {
            t.addMonsterById(o);
          }
        }
      }));
    };
    t.prototype.addMonsterById = function (e, t, o) {
      var i = this;
      undefined === o && (o = 0);
      if (3 == $2Cfg.Cfg.Monster.get(e.monId[0]).type) {
        return this.addBossById(e);
      }
      $2Game.Game.timer(function () {
        i.createMonster(e, t || i.randomPos).then(function (e) {
          e.toMove();
          i._batchSumCount--;
        });
      }, e.bronSpeed, e.Count);
    };
    t.prototype.addBossById = function (e) {
      var t = this;
      var o = $2Cfg.Cfg.Monster.get(e.monId[0]);
      var i = this.role.position.add(cc.v2(0, this.randomL));
      $2Game.Game.mgr.showEffectByType("entity/fight/effect/Effect_BossShow", {
        x: i.x,
        y: i.y
      }, true, 3, {
        parent: $2Game.Game.mgr.botEffectNode
      }).then(function (e) {
        e.node.scaleX = 2;
        $2Game.Game.warnTween(e.node);
      });
      $2NodePool.NodePool.spawn("entity/fight/" + this.MonsterType[o.type]).setNodeAssetFinishCall(function (o) {
        var n = o.getComponent($2MonsterTidalBoss.default);
        o.setAttribute({
          parent: t.game._entityNode,
          active: true,
          opacity: 255
        });
        n.setPosition(i);
        n.monsterId = +e.monsterID;
        n.lvCfg = e;
        n.init();
        n.steering.seekOn();
        n.steering.setTargetAgent1(t.mainRole);
        t.game._monsterMap.set(n.ID, n);
        t.game.elementMap.set(n.ID, n);
        t._batchSumCount--;
      });
    };
    return t;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = R;
})(exports.MBPack || (exports.MBPack = {}));