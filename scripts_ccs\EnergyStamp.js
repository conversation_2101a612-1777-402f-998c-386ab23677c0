var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_EnergyStamp = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.label = null;
    t.tick = 0;
    return t;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  _ctor.prototype.onLoad = function () {
    this.tickOffset = $2ModeBackpackHeroModel.default.instance.energyMinoffset;
  };
  Object.defineProperty(_ctor, "EnergyLimt", {
    get: function () {
      return 30 + ($2Manager.Manager.Shop.Subscrib_30 ? 10 : 0) + ($2Manager.Manager.Shop.Subscrib_Long ? 30 : 0);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.lateUpdate = function (e) {
    if ($2ModeBackpackHeroModel.default.instance.ischeckdone) {
      if ($2Manager.Manager.vo.knapsackVo.getVal($2CurrencyConfigCfg.CurrencyConfigDefine.Energy) >= o.EnergyLimt) {
        this.label.node.opacity = 0;
      } else {
        0 == $2Manager.Manager.vo.userVo.energy_refreshCd && ($2Manager.Manager.vo.userVo.energy_refreshCd = 60 * this.tickOffset);
        this.tick -= e;
        if (this.tick <= 0) {
          this.tick = 1;
          $2Manager.Manager.vo.userVo.energy_refreshCd--;
          this.label.node.opacity = 255;
          var t = $2GameUtil.GameUtil.formatSeconds($2Manager.Manager.vo.userVo.energy_refreshCd);
          this.label.string = t.str;
          $2Manager.Manager.vo.userVo.energy_refreshCd <= 0 && $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, 1);
          $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
          $2Manager.Manager.vo.saveUserData();
        }
      }
    }
  };
  _ctor.prototype.onDestroy = function () {
    $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
    $2Manager.Manager.vo.saveUserData();
    cc.log("exit:", $2Manager.Manager.vo.userVo.shop_refreshTimestamp);
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "label", undefined);
  return o = cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_EnergyStamp;