var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2AlertManager = require("AlertManager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_Commonguide = function (e) {
  function _ctor() {
    var t;
    var o = null !== e && e.apply(this, arguments) || this;
    o.focusArea = null;
    o.bgnode = null;
    o.handnode = null;
    o.desc = null;
    o.btnjump = null;
    o._cb = null;
    o.descPos = ((t = {})[0] = cc.v2(0, 475), t[1] = cc.v2(0, 0), t[2] = cc.v2(0, -475), t);
    return o;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  Object.defineProperty(_ctor, "isinitGuide", {
    get: function () {
      return this._isinitGuide;
    },
    set: function (e) {
      this._isinitGuide = e;
      e || $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GuideChange);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    this.offTouch();
  };
  _ctor.prototype.onShowFinish = function () {};
  _ctor.prototype.changeListener = function (e) {
    if (this._openArgs.param.isnewuser) {
      console.log("初始化新手引导");
      o.isinitGuide = true;
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Forcus, this.forcus, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Anim, this.animationto, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_OnlyDesc, this.descOnly, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Close, this.hideMask, this);
      this.btnjump.active = 1 == $2Manager.Manager.vo.switchVo.m20guideToggle[1];
    } else {
      $2Manager.Manager.vo.userVo.guideIndex = 17;
      $2Manager.Manager.vo.saveUserData();
    }
  };
  _ctor.prototype.onOpen = function () {
    this.guideList = $2Cfg.Cfg.BagGuide.getArray();
  };
  _ctor.prototype.onShow = function () {};
  _ctor.prototype.onHide = function () {};
  _ctor.prototype.onHideFinish = function () {};
  _ctor.prototype.setdesc = function () {
    var e;
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    this.desc.getComponentInChildren(cc.Button).interactable = false;
    var t = this.guideList[$2Manager.Manager.vo.userVo.guideIndex].btnClick;
    this.desc.getChildByName("fullbutton").active = t && 1 == t;
    this.desc.getChildByName("fullbutton").getComponent(cc.Button).interactable = t && 1 == t;
    this.desc.getChildByName("mask").active = false;
    this.desc.position = this.descPos[0];
    if (this.guideList[$2Manager.Manager.vo.userVo.guideIndex].desc) {
      this.desc.active = true;
      this.desc.getComponentInChildren(cc.Label).string = null === (e = this.guideList[$2Manager.Manager.vo.userVo.guideIndex]) || undefined === e ? undefined : e.desc;
    }
  };
  _ctor.prototype.descOnly = function (e, t) {
    console.log("descOnly", e);
    this._cb = t;
    this.setdesc();
    this.desc.getComponentInChildren(cc.Button).interactable = true;
    e && (this.desc.position = this.descPos[e]);
    this.desc.getChildByName("mask").active = true;
  };
  _ctor.prototype.animationto = function (e, t, o, i, n) {
    var r = this;
    undefined === o && (o = cc.v2(0, 0));
    undefined === n && (n = true);
    if (e && t) {
      this._cb = i;
      var a = e.wordPos;
      cc.Tween.stopAllByTarget(this.handnode);
      var s = t.wordPos;
      cc.tween(this.handnode).set({
        position: a.addSelf(cc.v2(60, 0))
      }).to(.5, {
        opacity: 255
      }).to(1, {
        position: s.addSelf(o.addSelf(cc.v2(60, 0)))
      }).union().repeat(3).call(function () {
        r.handnode.opacity = 0;
      }).start();
      n && $2Time.Time.delay(1.5, function () {
        cc.tween(r.handnode).to(.5, {
          scale: .8
        }).to(.5, {
          scale: 1
        }).union().repeatForever().start();
      });
      this.setdesc();
    } else {
      null == i || i();
    }
  };
  _ctor.prototype.jumpGuide = function () {
    $2Manager.Manager.vo.userVo.guideIndex = 17;
    $2Manager.Manager.vo.saveUserData();
    this.hideMask();
    this.close();
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_HandleButton, true, [0, 1, 2]);
  };
  _ctor.prototype.showJump = function () {
    var e = this;
    $2AlertManager.AlertManager.showAlert($2AlertManager.AlertType.SELECT, {
      desc: "是否跳过新手教程",
      confirm: function () {
        e.jumpGuide();
      }
    });
  };
  _ctor.prototype.hideMask = function () {
    var e;
    var t = this;
    $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Guide", {
      Step: $2Manager.Manager.vo.userVo.guideIndex
    });
    cc.Tween.stopAllByTarget(this.handnode);
    this.focusArea.active = false;
    this.focusArea.setContentSize(750, 1334);
    this.focusArea.setPosition(0, 0);
    $2Manager.Manager.vo.userVo.guideIndex++;
    $2Manager.Manager.vo.saveUserData();
    this.handnode.opacity = 0;
    this.node.getChildByName("block").active = false;
    this.desc.active = false;
    5 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_HandleButton, true, [0, 1]);
    if (15 == $2Manager.Manager.vo.userVo.guideIndex) {
      var o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).toggleContainer.toggleItems[2].node;
      this.forcus({
        targetNode: o,
        tweencb: function () {
          t.animationto(o, o, cc.v2(0, 0), null, true);
        }
      });
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
    null === (e = this._cb) || undefined === e || e.call(this);
    this._cb = null;
    17 == $2Manager.Manager.vo.userVo.guideIndex && this.close();
  };
  _ctor.prototype.forcus = function (e) {
    var t;
    if (e.targetNode) {
      e.offset || (e.offset = cc.Vec2.ZERO);
      e.descpos || (e.descpos = 0);
      this._cb = e.cb;
      this.focusArea.active = true;
      var o = e.targetNode.wordPos.addSelf(e.offset);
      cc.tween(this.focusArea).to(.5, {
        width: e.targetNode.width,
        height: e.targetNode.height,
        position: o
      }).call(function () {
        var t;
        null === (t = e.tweencb) || undefined === t || t.call(e);
      }).start();
      var i = this.node.getChildByName("block");
      i.active = e.blockevent;
      i.setContentSize(e.targetNode.getContentSize());
      i.setPosition(o);
      this.setdesc();
      this.desc.position = this.descPos[e.descpos];
      this.desc.getComponentInChildren(cc.Button).interactable = e.enableclick;
    } else {
      null === (t = e.cb) || undefined === t || t.call(e);
    }
  };
  _ctor.prototype.onBtn = function () {};
  _ctor.prototype.close = function () {
    e.prototype.close.call(this);
  };
  _ctor.prototype.onClose = function () {
    o.isinitGuide = false;
  };
  _ctor._isinitGuide = false;
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "focusArea", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "bgnode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "handnode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "desc", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnjump", undefined);
  return o = cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Common/Commonguide"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Guide), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Guide)], _ctor);
}($2MVC.MVC.BaseView);
exports.default = def_Commonguide;