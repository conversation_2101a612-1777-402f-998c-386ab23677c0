// SkillManager.ts
// 从 SkillManager.js 转换而来

// 管理器类
export class SkillManager {
    private static instance: SkillManager;

    public static getInstance(): SkillManager {
        if (!this.instance) {
            this.instance = new SkillManager();
        }
        return this.instance;
    }

    private constructor() {
        this.init();
    }

    private init(): void {
        // TODO: 初始化管理器
    }

    // TODO: 添加管理器方法
}