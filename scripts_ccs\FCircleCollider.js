var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2FCollider = require("FCollider");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_FCircleCollider = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.worldPosition = cc.v2();
    t.worldRadius = 0;
    t._radius = 40;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "type", {
    get: function () {
      return $2FCollider.ColliderType.Circle;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "radius", {
    get: function () {
      return this._radius;
    },
    set: function (e) {
      this._radius = e < 0 ? 0 : e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setSize = function (e) {
    this.radius = e.width / 2;
  };
  cc__decorate([ccp_property], _ctor.prototype, "radius", null);
  cc__decorate([ccp_property], _ctor.prototype, "_radius", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("碰撞组件Ex/FCircleCollider")], _ctor);
}($2FCollider.default);
exports.default = def_FCircleCollider;