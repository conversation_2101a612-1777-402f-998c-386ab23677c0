var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();
var def_BoomerangBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.isBoomerang = false;
    t.isCcomeback = false;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this.isCcomeback = false;
    this.isBoomerang = !!this.vo.ower.buffMgr.isHasID([11600, 11408, 10160]);
    this.vo.lifeTime = this.isBoomerang ? 10 : 3;
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (!(this._vo.lifeTime < 0 || this.isDead)) {
      cc.Vec2.multiplyScalar(u, this._vo.shootDir, this.maxSpeed * t);
      cc.Vec2.add(u, this.position, u);
      this.setPosition(u);
      if (!this.isCcomeback && this.isBoomerang && cc.Vec2.squaredDistance(this.vo.shootDir.mul(this.vo.belongSkill.dis).add(this.vo.ower.position).mul(1.3), this.position) < Math.pow(100, 2)) {
        this.vo.shootDir.set(this.vo.ower.position.sub(this.position).normalizeSelf()), this.isCcomeback = true;
      }
      this.isCcomeback && cc.Vec2.squaredDistance(this.vo.startPos, this.position) < Math.pow(50, 2) && (this.vo.lifeTime = 0);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/BoomerangBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_BoomerangBullet;