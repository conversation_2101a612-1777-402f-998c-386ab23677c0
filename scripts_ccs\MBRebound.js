var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MBRebound = undefined;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2FPolygonCollider = require("FPolygonCollider");
var $2Intersection = require("Intersection");
var $2GameUtil = require("GameUtil");
var $2RecordVo = require("RecordVo");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2CompManager = require("CompManager");
var $2MTKnife = require("MTKnife");
var $2NodePool = require("NodePool");
var $2ModeBulletsReboundModel = require("ModeBulletsReboundModel");
var $2MBRMonster = require("MBRMonster");
var $2MBRRole = require("MBRRole");
(function (e) {
  var t;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.MyRound = 1] = "MyRound";
    e[e.BulletAnim = 2] = "BulletAnim";
    e[e.EnemyRound = 3] = "EnemyRound";
    e[e.Determine = 4] = "Determine";
  })(t = e.RoundStatus || (e.RoundStatus = {}));
  var o;
  var i = function (e) {
    function t() {
      var t;
      var i = null !== e && e.apply(this, arguments) || this;
      i.poolADMap = ((t = {})[o.NormalBuff] = {
        resetNum: 3,
        getAll: 0
      }, t[o.HighBuff] = {
        resetNum: 1,
        getAll: 0
      }, t);
      i.freeTime = 1;
      i.killNum = 0;
      i.countdownTime = 0;
      i.adRefreshEquip = 9999;
      i.ADNum = 0;
      return i;
    }
    cc__extends(t, e);
    return t;
  }($2RecordVo.RecordVo.Data);
  e.RecordData = i;
  (function (e) {
    e[e.NormalBuff = 1] = "NormalBuff";
    e[e.HighBuff = 2] = "HighBuff";
  })(o = e.poolType || (e.poolType = {}));
  var S = function (t) {
    function o(e) {
      var o = t.call(this, e) || this;
      o.cameraZoomRatio = 1;
      o.recordVo = new $2RecordVo.RecordVo.Mgr("MBRebound", function () {
        return new i();
      });
      o.passParam = e;
      return o;
    }
    cc__extends(o, t);
    Object.defineProperty(o.prototype, "mode", {
      get: function () {
        return $2ModeBulletsReboundModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "rVo", {
      get: function () {
        return this.recordVo.vo;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.loadMap = function (e, t) {
      var o = this;
      this.gameNode = e;
      this.miniGameCfg = $2Cfg.Cfg.MiniGameLv.get(this.passParam.id);
      this._entityNode = e.getChildByName("entityNode");
      this._bulletNode = e.getChildByName("bulletNode");
      this._mapNode = e.getChildByName("mapNode");
      this._botEffectNode = e.getChildByName("botEffectNode");
      this._topEffectNode = e.getChildByName("topEffectNode");
      this.LifeBarUI = e.getORaddChildByName("LifeBarUI");
      this.topUINode = e.getORaddChildByName("topUINode");
      this.behitUI = e.getORaddChildByName("behitUI");
      this._finishCall = t;
      $2Manager.Manager.setPhysics(true, false);
      $2Manager.Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then(function (e) {
        e.setAttribute({
          parent: o._mapNode,
          zIndex: -1
        });
        o.myTiledMap = o._mapNode.getComByChild(cc.TiledMap);
        var t = o.getObjPos(o.myTiledMap.getObjectGroup("born").getObject("player"));
        o.createRole(t).then(function () {
          var e = o.getObjPos(o.myTiledMap.getObjectGroup("born").getObject("monster"));
          o.createMonster($2Cfg.Cfg.bagMonsterLv.find({
            lv: o.miniGameCfg.lvid
          }), e).then(function (e) {
            o.bronMonsterMgr.changeGameStatus($2MTKnife.MTKnife.RoundStatus.MyRound);
            e.roleNode.scaleX *= e.position.x > o.mainRole.position.x ? -1 : 1;
            o.mainRole.roleNode.scaleX *= e.position.x > o.mainRole.position.x ? 1 : -1;
          });
          var t = o.myTiledMap.getObjectGroup("born").getObjects().find(function (e) {
            return e.name.includes("box");
          });
          if (t) {
            var i = o.getObjPos(t);
            o.createBox($2Cfg.Cfg.bagMonsterLv.get(t.name.split("box")[1]), i);
          }
        });
        o.physicsPolygonColliders = o.myTiledMap.getComponents(cc.PhysicsPolygonCollider);
        var i = o.myTiledMap.getComponent($2FPolygonCollider.default);
        i.points = [];
        o.physicsPolygonColliders.forEach(function (e) {
          e.points.forEach(function (e) {
            return i.points.push(e);
          });
        });
      });
      this.gameCamera.setZoomRatio(this.cameraZoomRatio);
      this.gameCamera.setPosition(cc.Vec2.ZERO);
      this.bronMonsterMgr = this.gameNode.getORaddComponent(P);
      this.bronMonsterMgr.init();
      this._finishCall && this._finishCall();
    };
    o.prototype.getObjPos = function (e) {
      return cc.v2(e.x - this.myTiledMap.node.width / 2 + e.width / 2, e.y - this.myTiledMap.node.height / 2 - e.height);
    };
    o.prototype.gameEnd = function () {
      var e = this;
      $2Game.Game.timerOnce(function () {
        e.gameState = $2Game.Game.State.NONE;
      }, .5);
    };
    o.prototype.createRole = function (e) {
      var t = this;
      return new Promise(function (o) {
        $2NodePool.NodePool.spawn("entity/fight/ModeBulletsRebound/role").setNodeAssetFinishCall(function (i) {
          var n = i.getComponent($2MBRRole.default);
          i.parent = t._entityNode;
          n.setPosition(e);
          n.init();
          n.setRole();
          $2CompManager.default.Instance.registerComp(n);
          t.mainRole = n;
          o(n);
        });
      });
    };
    o.prototype.gamePause = function (e) {
      t.prototype.gamePause.call(this, e);
      $2Manager.Manager.setPhysics(!e);
    };
    o.prototype.spawnBullet = function (o, i, n) {
      var r = this;
      undefined === n && (n = {});
      return new Promise(function (a) {
        t.prototype.spawnBullet.call(r, o, i, n).then(function (t) {
          r.curBullet = t;
          a(t);
          r.bronMonsterMgr.changeGameStatus(e.RoundStatus.BulletAnim);
        });
      });
    };
    o.prototype.createMonster = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        var n = $2Cfg.Cfg.Monster.get(e.monId[0]);
        $2NodePool.NodePool.spawn("entity/fight/ModeBulletsRebound/monster").setNodeAssetFinishCall(function (r) {
          if (!r) {
            return console.error("怪物生成错误", null == n ? undefined : n.name);
          }
          var s = r.getComponent($2MBRMonster.default);
          o.monster = s;
          r.parent = o._entityNode;
          s.setPosition(t);
          s.monsterId = n.id;
          s.lvCfg = e;
          s.init();
          o._monsterMap.set(s.ID, s);
          o.elementMap.set(s.ID, s);
          i(s);
          $2Notifier.Notifier.send($2ListenID.ListenID.Game_LoadFinish);
        });
      });
    };
    o.prototype.createBox = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        var n = $2Cfg.Cfg.Monster.get(e.monId[0]);
        $2NodePool.NodePool.spawn("entity/fight/ModeBulletsRebound/monster").setNodeAssetFinishCall(function (r) {
          if (!r) {
            return console.error("[createBox]生成错误", null == n ? undefined : n.name);
          }
          var a = r.getComponent($2MBRMonster.default);
          r.parent = o._entityNode;
          a.setPosition(t);
          a.monsterId = n.id;
          a.lvCfg = e;
          a.init();
          o._monsterMap.set(a.ID, a);
          o.elementMap.set(a.ID, a);
          i(a);
        });
      });
    };
    Object.defineProperty(o.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    return o;
  }($2Game.Game.Mgr);
  e.Mgr = S;
  cc.v2();
  e.maxPower = 200;
  var k = function (e) {
    function t() {
      var t = e.call(this) || this;
      t.bulletNum = t.game.miniGameCfg.bullet;
      t.fireNum = 0;
      t.line = t.game.botEffectNode.getComByChild(cc.Sprite, "line");
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "aimData", {
      get: function () {
        return this._aimData;
      },
      set: function (e) {
        this._aimData = e;
        var t = this.ower.bodyPosition;
        var o = $2GameUtil.GameUtil.AngleAndLenToPos((e.angle + 360) % 360, 2e3).add(this.ower.bodyPosition);
        var i = [];
        this.game.physicsPolygonColliders.forEach(function (e) {
          e.points.forEach(function (n, r) {
            var a = $2Intersection.Intersection.getLineSegmentIntersection(t, o, n, e.points[r + 1] || e.points[0]);
            a && i.push({
              pos: a,
              d: cc.Vec2.squaredDistance(t, a)
            });
          });
        });
        i.sort(function (e, t) {
          return e.d - t.d;
        });
        i[0] && o.set(i[0].pos);
        this.line.node.setAttribute({
          active: true,
          position: this.ower.bodyPosition,
          angle: (e.angle + 360) % 360,
          height: cc.Vec2.distance(t, o)
        });
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.fire = function (e) {
      var t = this;
      undefined === e && (e = 0);
      this.line.node.setActive(false);
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, true);
      this.ower.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(this.aimData.angle % 360));
      this.ower.scheduleOnce(function () {
        t.ower.firstSkill.checkTarget();
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, false);
      }, e);
      this.fireNum++;
      this.ower.mySkeleton.playQueue(["attack", "idle"], true);
      this.bulletNum--;
    };
    return t;
  }($2GameSeting.GameSeting.CompBase);
  e.KnifeController = k;
  var P = function (e) {
    function o() {
      var o = null !== e && e.apply(this, arguments) || this;
      o._batchNum = 0;
      o.buffOffset = 0;
      o.cutStaus = t.NONE;
      o.countDown = 0;
      return o;
    }
    cc__extends(o, e);
    Object.defineProperty(o.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        this._batchNum = e;
        0 != e && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.init = function () {};
    Object.defineProperty(o.prototype, "curBullet", {
      get: function () {
        return this.game.curBullet;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.onUpdate = function () {
      var e;
      this.game.gameState == $2Game.Game.State.START && this.cutStaus == t.BulletAnim && ((null === (e = this.curBullet) || undefined === e ? undefined : e.isActive) || this.changeGameStatus(t.Determine));
    };
    Object.defineProperty(o.prototype, "role", {
      get: function () {
        return this.game.mainRole;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.changeListener = function (t) {
      e.prototype.changeListener.call(this, t);
    };
    o.prototype.changeGameStatus = function (e) {
      var o = this;
      switch (e) {
        case t.MyRound:
          if (0 == this.role.knifeController.bulletNum) {
            this.game.sendEvent("BatchFail");
            return void $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
          }
          this.cutRoundType = e;
          $2AlertManager.AlertManager.showNormalTips("我方回合");
          break;
        case t.EnemyRound:
          if (!this.game.monster.firstSkill) {
            return void this.changeGameStatus(t.MyRound);
          }
          this.cutRoundType = e;
          this.game.monster.aiFire();
          $2AlertManager.AlertManager.showNormalTips("敌方回合");
          break;
        case t.Determine:
          this.game.physicsPolygonColliders.forEach(function (e) {
            e.restitution = 0;
          });
          this.scheduleOnce(function () {
            if (o.role.isActive) {
              if (o.game.monster.isActive) {
                o.changeGameStatus(o.cutRoundType == t.MyRound ? t.EnemyRound : t.MyRound);
              } else {
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, true);
              }
            } else {
              o.game.sendEvent("BatchFail");
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
            }
          }, 1);
          break;
        case t.BulletAnim:
      }
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRoundType, e);
      this.cutStaus = e;
    };
    return o;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = P;
})(exports.MBRebound || (exports.MBRebound = {}));