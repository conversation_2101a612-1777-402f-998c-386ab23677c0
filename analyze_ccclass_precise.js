const fs = require('fs');
const path = require('path');

// 更精确地分析文件是否为 ccclass
function analyzeFileForCcclass(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查是否包含 Cocos Creator 相关的特征
        const ccclassIndicators = [
            // 直接的 ccclass 装饰器
            /@ccclass/,
            // 编译后的装饰器调用
            /__decorate\s*\(\s*\[\s*ccclass/,
            // cc.Class 调用
            /cc\.Class\s*\(/,
            // 继承自 cc.Component
            /cc\.Component/,
            // 继承自 cc.Node
            /cc\.Node/,
            // Cocos Creator 属性装饰器
            /@property/,
            // cc._decorator
            /cc\._decorator/
        ];
        
        // 检查是否包含 Cocos Creator 生命周期方法
        const lifecycleMethods = [
            /onLoad\s*[:=]\s*function/,
            /start\s*[:=]\s*function/,
            /update\s*[:=]\s*function/,
            /lateUpdate\s*[:=]\s*function/,
            /onEnable\s*[:=]\s*function/,
            /onDisable\s*[:=]\s*function/,
            /onDestroy\s*[:=]\s*function/
        ];
        
        // 检查是否为纯配置文件或工具文件
        const nonCcclassIndicators = [
            // 只有常量导出
            /^Object\.defineProperty\(exports[^}]+\};\s*exports\.\w+\s*=\s*[^;]+;\s*$/m,
            // API 函数模块
            /exports\.\w+\s*=\s*function\s*\(/,
            // 纯配置数据
            /exports\.\w+\s*=\s*["'\d]/,
            // SDK 相关
            /window\.(wx|tt|qq|qg|ks)/,
            // 网络请求相关
            /\$2Request\.default\.post/
        ];
        
        let ccclassScore = 0;
        let nonCcclassScore = 0;
        
        // 计算 ccclass 特征分数
        ccclassIndicators.forEach(pattern => {
            if (pattern.test(content)) {
                ccclassScore += 2;
            }
        });
        
        lifecycleMethods.forEach(pattern => {
            if (pattern.test(content)) {
                ccclassScore += 1;
            }
        });
        
        // 计算非 ccclass 特征分数
        nonCcclassIndicators.forEach(pattern => {
            if (pattern.test(content)) {
                nonCcclassScore += 1;
            }
        });
        
        // 查找类定义
        const expClassMatches = content.match(/var\s+exp_(\w+)\s*=\s*function\s*\(/g) || [];
        const exportsMatches = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];
        
        const expClassNames = expClassMatches.map(match => {
            const nameMatch = match.match(/var\s+exp_(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);
        
        const exportClassNames = exportsMatches.map(match => {
            const nameMatch = match.match(/exports\.(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);
        
        const result = {
            filePath: filePath,
            fileName: path.basename(filePath),
            expClassCount: expClassMatches.length,
            exportsCount: exportsMatches.length,
            expClassNames: expClassNames,
            exportClassNames: exportClassNames,
            ccclassScore: ccclassScore,
            nonCcclassScore: nonCcclassScore,
            isSingleClass: (expClassMatches.length + exportsMatches.length) === 1,
            isProbablyCcclass: ccclassScore > 0 && ccclassScore > nonCcclassScore,
            isDefinitelyNotCcclass: nonCcclassScore > ccclassScore && nonCcclassScore > 0
        };
        
        return result;
    } catch (error) {
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            error: error.message,
            isSingleClass: false,
            isProbablyCcclass: false
        };
    }
}

// 分析 scripts_ccs 文件夹中的文件
function analyzeScriptsCcs() {
    const scriptsDir = './scripts_ccs';
    
    if (!fs.existsSync(scriptsDir)) {
        console.log('scripts_ccs 文件夹不存在');
        return;
    }
    
    const files = fs.readdirSync(scriptsDir);
    const jsFiles = files.filter(file => file.endsWith('.js')).map(file => path.join(scriptsDir, file));
    
    console.log(`分析 scripts_ccs 文件夹中的 ${jsFiles.length} 个文件...\n`);
    
    const probablyCcclassFiles = [];
    const definitelyNotCcclassFiles = [];
    const uncertainFiles = [];
    
    jsFiles.forEach(filePath => {
        const analysis = analyzeFileForCcclass(filePath);
        
        if (analysis.error) {
            console.log(`错误: ${analysis.fileName} - ${analysis.error}`);
            return;
        }
        
        const className = analysis.expClassNames[0] || analysis.exportClassNames[0] || '未知';
        
        if (analysis.isDefinitelyNotCcclass) {
            definitelyNotCcclassFiles.push(analysis);
            console.log(`✗ 非ccclass: ${analysis.fileName} - ${className} (非ccclass分数: ${analysis.nonCcclassScore})`);
        } else if (analysis.isProbablyCcclass) {
            probablyCcclassFiles.push(analysis);
            console.log(`✓ 可能ccclass: ${analysis.fileName} - ${className} (ccclass分数: ${analysis.ccclassScore})`);
        } else {
            uncertainFiles.push(analysis);
            console.log(`? 不确定: ${analysis.fileName} - ${className} (ccclass: ${analysis.ccclassScore}, 非ccclass: ${analysis.nonCcclassScore})`);
        }
    });
    
    console.log(`\n=== 重新分析结果 ===`);
    console.log(`可能是 ccclass: ${probablyCcclassFiles.length} 个`);
    console.log(`确定不是 ccclass: ${definitelyNotCcclassFiles.length} 个`);
    console.log(`不确定: ${uncertainFiles.length} 个`);
    
    if (definitelyNotCcclassFiles.length > 0) {
        console.log(`\n=== 确定不是 ccclass 的文件 ===`);
        definitelyNotCcclassFiles.forEach(file => {
            const className = file.expClassNames[0] || file.exportClassNames[0] || '未知';
            console.log(`${file.fileName} - ${className}`);
        });
    }
    
    // 保存重新分析的结果
    const result = {
        probablyCcclassFiles: probablyCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.expClassNames[0] || f.exportClassNames[0] || '未知',
            ccclassScore: f.ccclassScore
        })),
        definitelyNotCcclassFiles: definitelyNotCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.expClassNames[0] || f.exportClassNames[0] || '未知',
            nonCcclassScore: f.nonCcclassScore
        })),
        uncertainFiles: uncertainFiles.map(f => ({
            fileName: f.fileName,
            className: f.expClassNames[0] || f.exportClassNames[0] || '未知',
            ccclassScore: f.ccclassScore,
            nonCcclassScore: f.nonCcclassScore
        }))
    };
    
    fs.writeFileSync('ccclass_precise_analysis.json', JSON.stringify(result, null, 2));
    console.log(`\n精确分析结果已保存到: ccclass_precise_analysis.json`);
}

analyzeScriptsCcs();
