var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2SoundCfg = require("SoundCfg");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2BaseEntity = require("BaseEntity");
var $2Game = require("Game");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_Bullet_RigidBody = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this.myRigidBody = this.getComponent(cc.RigidBody);
  };
  _ctor.prototype.setBulletVo = function (t) {
    var o = this;
    e.prototype.setBulletVo.call(this, t);
    3 == t.skillCfg.object && this.delayByGame(function () {
      o.vo.ignore.delete(o.vo.ower.ID);
    }, .5);
    this.myRigidBody.linearVelocity = t.shootDir.mul(6 * t.speed);
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (this.isActive && !this.isBanRotate && 0 == this.isRotate) {
      this.myRigidBody.angularVelocity = 0;
      var o = $2GameUtil.GameUtil.GetAngle(this.myRigidBody.linearVelocity.normalize()) + 90;
      this.node.angle = o;
    }
  };
  _ctor.prototype.onCollisionEnter = function (t, o) {
    if (!this.isDead) {
      if (t.node.name.includes("map")) {
        if ($2Game.Game.Mode.MANGUARDS == $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode)) {
          return void (this._vo.lifeTime = 0);
        }
        if (this.vo.belong == $2BaseEntity.EntityType.Role) {
          $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_bulletreound);
        } else {
          $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_knifereound);
        }
      }
      e.prototype.onCollisionEnter.call(this, t, o);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_RigidBody")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_RigidBody;