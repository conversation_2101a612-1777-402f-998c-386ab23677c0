var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20Gooditem = require("M20Gooditem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_Pop_ShopBuyConfirm = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.shownode = null;
    t.cost = null;
    t.costT = null;
    t.desc = null;
    t.title = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    var e = this;
    var t = this.param.cfg;
    var o = $2Cfg.Cfg.RoleUnlock.find({
      id: t.equipId
    });
    var i = this.param.disCount;
    var n = this.param.count || t.getNum;
    var r = $2Cfg.Cfg.CurrencyConfig.get(t.type);
    $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (i) {
      var r = i;
      r.setParent(e.shownode);
      var s = r.getComponent($2M20Gooditem.default);
      s.setdata({
        path: o ? o.icon : t.icon,
        bgpath: o ? $2GameSeting.GameSeting.getRarity(e.mode.buffmap[o.rarity]).blockImg : t.bgpath,
        count: n,
        isfrag: !!o
      });
      s.goodsbg.node.setActive(!!o);
    });
    this.cost.string = (o ? Math.floor(t.costVal[0] * i) : " 获得") + "";
    var s = $2Cfg.Cfg.CurrencyConfig.find({
      id: t.costType[0]
    });
    $2Manager.Manager.loader.loadSpriteToSprit(s.icon, this.costT, this.node);
    // this.costT.node.scale = 22 == t.type ? 1 : .6;
    this.desc.string = o ? o.desc : r.desc;
    this.title.string = t.title;
  };
  _ctor.prototype.buyIt = function () {
    var e;
    var t;
    $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 3);
    null === (t = (e = this.param).cb) || undefined === t || t.call(e);
    this.close();
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "shownode", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "cost", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "costT", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "desc", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "title", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_ShopBuyConfirm"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_ShopBuyConfirm;