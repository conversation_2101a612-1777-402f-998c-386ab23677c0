var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2FCollider = require("FCollider");
var $2Intersection = require("Intersection");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_FPolygonCollider = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.worldPoints = [cc.v2(-100, 0), cc.v2(0, 50), cc.v2(100, 0)];
    t.worldEdge = [];
    t._points = [cc.v2(-50, -50), cc.v2(50, -50), cc.v2(50, 50), cc.v2(-50, 50)];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "type", {
    get: function () {
      return $2FCollider.ColliderType.Polygon;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "points", {
    get: function () {
      return this._points;
    },
    set: function (e) {
      this._points = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initCollider = function () {
    e.prototype.initCollider.call(this);
    this.isConvex = !$2Intersection.Intersection.isConcavePolygon(this.points);
  };
  cc__decorate([ccp_property({
    type: [cc.Vec2]
  })], _ctor.prototype, "_points", undefined);
  cc__decorate([ccp_property({
    type: [cc.Vec2]
  })], _ctor.prototype, "points", null);
  return cc__decorate([ccp_ccclass, ccp_menu("碰撞组件Ex/FPolygonCollider")], _ctor);
}($2FCollider.default);
exports.default = def_FPolygonCollider;