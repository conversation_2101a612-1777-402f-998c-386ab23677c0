{"timestamp": "2025-07-09T03:15:21.283Z", "cleanedFiles": [{"fileName": "Buff.js", "className": "Buff", "supportingMatches": [], "lifecycleMatches": ["生命周期1"], "totalScore": 1}, {"fileName": "BulletVoPool.js", "className": "BulletVoPool", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "Cfg.js", "className": "Cfg", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "FColliderManager.js", "className": "FColliderManager", "supportingMatches": ["支持特征7"], "lifecycleMatches": ["生命周期3"], "totalScore": 3}, {"fileName": "KnapsackVo.js", "className": "KnapsackVo", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "LatticeMap.js", "className": "LatticeMap", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "LevelMgr.js", "className": "Level", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MBackpackHero.js", "className": "MBPack", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MBRebound.js", "className": "MBRebound", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MCBossState.js", "className": "MCBossState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MChains.js", "className": "<PERSON><PERSON><PERSON>", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MMGuards.js", "className": "<PERSON><PERSON><PERSON><PERSON>", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MonstarTideDragon.js", "className": "MonstarTideDragon", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterElite.js", "className": "MonsterElite", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterState.js", "className": "MonsterState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MonsterTidal.js", "className": "MonsterTidal", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterTidalBoss.js", "className": "MonsterTidalBoss", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterTidalState.js", "className": "MonsterTidalState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MTideDefendRebound.js", "className": "MTideDefendRebound", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MTKnife.js", "className": "MTKnife", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "PetState.js", "className": "PetState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "PropertyVo.js", "className": "Property", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "RBadgeModel.js", "className": "RBadgeModel", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "RewardEvent.js", "className": "RewardEvent", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "RoleState.js", "className": "RoleState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "SkillManager.js", "className": "Skill", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "TaskModel.js", "className": "TaskModel", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "TrackManger.js", "className": "TrackManger", "supportingMatches": [], "lifecycleMatches": ["生命周期7"], "totalScore": 1}, {"fileName": "Vehicle.js", "className": "Vehicle", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}], "backupDirectory": "./scripts_ccs_false_backup", "successCount": 29, "failCount": 0, "remainingTrueCcclassFiles": 114, "totalCleaned": 29}