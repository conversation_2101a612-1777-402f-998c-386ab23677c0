var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_LigatureBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.connect = new Set();
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this.connect.clear();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/LigatureBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_LigatureBullet;