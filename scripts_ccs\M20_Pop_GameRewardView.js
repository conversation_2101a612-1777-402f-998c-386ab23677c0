var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20Gooditem = require("M20Gooditem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_Pop_GameRewardView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.rewardNode = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.onOpen = function () {
    var e = this;
    $2Manager.Manager.loader.loadPrefabRes("ui/ModeBackpackHero/goodItem").then(function (t) {
      e.param.data.forEach(function (o, i) {
        var n = cc.instantiate(t);
        n.setParent(e.rewardNode);
        cc.tween(n).set({
          opacity: 0
        }).delay(.2 * i).to(.2, {
          opacity: 255
        }).start();
        var r = "";
        var c = $2GameSeting.GameSeting.getRarity(o.rarity).blockImg;
        if (o.type == $2GameSeting.GameSeting.GoodsType.Fragment) {
          var l = $2Cfg.Cfg.RoleUnlock.find({
            id: o.id
          });
          r = l.icon;
          c = $2GameSeting.GameSeting.getRarity(l.rarity).blockImg;
        } else {
          r = $2Cfg.Cfg.CurrencyConfig.get(o.id).icon;
        }
        n.getComponent($2M20Gooditem.default).setdata({
          path: r,
          bgpath: c,
          count: o.num,
          isfrag: o.type == $2GameSeting.GameSeting.GoodsType.Fragment
        });
      });
    });
  };
  _ctor.prototype.onClose = function () {};
  _ctor.prototype.setInfo = function () {
    $2Manager.Manager.audio.playAudio(2002);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "rewardNode", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_GameRewardView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Guide), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_GameRewardView;