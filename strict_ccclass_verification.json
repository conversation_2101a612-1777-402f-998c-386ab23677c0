{"trueCcclassFiles": [{"fileName": "ArcBullet.js", "className": "ArcBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "AutoAmTool.js", "className": "AutoAmTool", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期5"], "totalScore": 14}, {"fileName": "AutoAnimationClip.js", "className": "AutoAnimationClip", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期5", "生命周期6"], "totalScore": 15}, {"fileName": "AutoFollow.js", "className": "AutoFollow", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期3"], "totalScore": 13}, {"fileName": "BackHeroProp.js", "className": "BackHeroProp", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "BackpackHeroHome.js", "className": "BackpackHeroHome", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征7"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "BoomerangBullet.js", "className": "BoomerangBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "BounceBullet.js", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "BuffCardItem.js", "className": "BuffCardItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "Bullet.js", "className": "Bullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "BulletBase.js", "className": "BulletBase", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期1"], "totalScore": 11}, {"fileName": "Bullet_Arrow.js", "className": "Bullet_Arrow", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_FollowTarget.js", "className": "Bullet_FollowTarget", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_HitReflex.js", "className": "Bullet_HitReflex", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_Laser.js", "className": "Bullet_Laser", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_Ligature.js", "className": "Bullet_Ligature", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "Bullet_LigaturePonit.js", "className": "Bullet_LigaturePonit", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_Path.js", "className": "Bullet_Path", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_RandomMove.js", "className": "Bullet_RandomMove", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Bullet_RigidBody.js", "className": "Bullet_RigidBody", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期1"], "totalScore": 9}, {"fileName": "CircleBullet.js", "className": "CircleBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Commonguide.js", "className": "Commonguide", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "ContinuousBullet.js", "className": "Continuous<PERSON>ullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "DialogBox.js", "className": "DialogBox", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "DragonBody.js", "className": "DragonBody", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": ["生命周期1", "生命周期7"], "totalScore": 10}, {"fileName": "EffectSkeleton.js", "className": "EffectSkeleton", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期1", "生命周期5"], "totalScore": 12}, {"fileName": "Effect_Behead.js", "className": "Effect_Behead", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "Effect_Behit.js", "className": "Effect_Behit", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "EnergyStamp.js", "className": "EnergyStamp", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期4", "生命周期7"], "totalScore": 11}, {"fileName": "EntityDieEffect.js", "className": "EntityDieEffect", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "ExchangeCodeView.js", "className": "ExchangeCodeView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "FBoxCollider.js", "className": "FBoxCollider", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "FCircleCollider.js", "className": "FCircleCollider", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "FCollider.js", "className": "FCollider", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 12}, {"fileName": "FPolygonCollider.js", "className": "FPolygonCollider", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "GameAnimi.js", "className": "GameAnimi", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期5"], "totalScore": 11}, {"fileName": "GameEffect.js", "className": "GameEffect", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "GameSkeleton.js", "className": "GameSkeleton", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期1", "生命周期5", "生命周期6", "生命周期7"], "totalScore": 14}, {"fileName": "GiftPackView.js", "className": "GiftPackView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "Goods.js", "className": "Goods", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征7"], "lifecycleMatches": [], "totalScore": 6}, {"fileName": "GoodsUIItem.js", "className": "GoodsUIItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期5", "生命周期6"], "totalScore": 15}, {"fileName": "GridView.js", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期5", "生命周期7"], "totalScore": 16}, {"fileName": "GridViewCell.js", "className": "Grid<PERSON>iew<PERSON>ell", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征6"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 8}, {"fileName": "LaserRadiationBullet.js", "className": "LaserRadiationBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Launcher.js", "className": "Launcher", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期1", "生命周期2", "生命周期3", "生命周期4", "生命周期5", "生命周期6"], "totalScore": 16}, {"fileName": "LifeBar.js", "className": "LifeBar", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征6"], "lifecycleMatches": [], "totalScore": 6}, {"fileName": "LifeLabel.js", "className": "LifeLabel", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征6"], "lifecycleMatches": [], "totalScore": 6}, {"fileName": "LigatureBullet.js", "className": "LigatureBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M20Equipitem.js", "className": "M20Equipitem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "M20EquipitemBlock.js", "className": "M20EquipitemBlock", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4"], "lifecycleMatches": [], "totalScore": 6}, {"fileName": "M20EquipitemList.js", "className": "M20EquipitemList", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征7"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 10}, {"fileName": "M20Gooditem.js", "className": "M20Gooditem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M20Prop.js", "className": "M20Prop", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征7"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M20Prop_Equip.js", "className": "M20Prop_Equip", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": ["生命周期3"], "totalScore": 5}, {"fileName": "M20Prop_Gemstone.js", "className": "M20Prop_Gemstone", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": ["生命周期3"], "totalScore": 5}, {"fileName": "M20_PartItem.js", "className": "M20_PartItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期2", "生命周期3", "生命周期5", "生命周期6", "生命周期7"], "totalScore": 15}, {"fileName": "M20_Pop_EquipInfo.js", "className": "M20_Pop_EquipInfo", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_Pop_GameRewardView.js", "className": "M20_Pop_GameRewardView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_Pop_GetBox.js", "className": "M20_Pop_GetBox", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_Pop_GetEnergy.js", "className": "M20_Pop_GetEnergy", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_Pop_Insufficient_Props_Tips.js", "className": "M20_Pop_Insufficient_Props_Tips", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M20_Pop_NewEquipUnlock.js", "className": "M20_Pop_NewEquipUnlock", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "M20_Pop_ShopBoxInfo.js", "className": "M20_Pop_ShopBoxInfo", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_Pop_ShopBuyConfirm.js", "className": "M20_Pop_ShopBuyConfirm", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_PrePare_Activity.js", "className": "M20_PrePare_Activity", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": ["生命周期5"], "totalScore": 11}, {"fileName": "M20_PrePare_Equip.js", "className": "M20_PrePare_Equip", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_PrePare_Fight.js", "className": "M20_PrePare_Fight", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_PrePare_MenuView.js", "className": "M20_PrePare_MenuView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M20_PrePare_Shop.js", "className": "M20_PrePare_Shop", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": ["生命周期1"], "totalScore": 13}, {"fileName": "M20_ShopPartItem.js", "className": "M20_ShopPartItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 12}, {"fileName": "M20_ShopPartItem_adcoupon.js", "className": "M20_ShopPartItem_adcoupon", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "M20_ShopPartItem_box.js", "className": "M20_ShopPartItem_box", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征7"], "lifecycleMatches": ["生命周期1", "生命周期7"], "totalScore": 8}, {"fileName": "M20_ShopPartItem_coin.js", "className": "M20_ShopPartItem_coin", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "M20_ShopPartItem_daily.js", "className": "M20_ShopPartItem_daily", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征7"], "lifecycleMatches": ["生命周期3"], "totalScore": 9}, {"fileName": "M20_ShopPartItem_hero.js", "className": "M20_ShopPartItem_hero", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "M20_Shop_HeroItem.js", "className": "M20_Shop_HeroItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": ["生命周期7"], "totalScore": 9}, {"fileName": "M33_FightBuffView.js", "className": "M33_FightBuffView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "M33_Pop_DiffSelectGeneral.js", "className": "M33_Pop_DiffSelectGeneral", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M33_Pop_GameEnd.js", "className": "M33_Pop_GameEnd", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "M33_Pop_Revive.js", "className": "M33_Pop_Revive", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "M33_TestBox.js", "className": "M33_TestBox", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期1"], "totalScore": 13}, {"fileName": "MBRMonster.js", "className": "MBRMonster", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期3"], "totalScore": 9}, {"fileName": "MBRRole.js", "className": "MBRRole", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "MCPet.js", "className": "MCPet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "MCRole.js", "className": "MCRole", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "MMGMonster.js", "className": "MMGMonster", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "MMGRole.js", "className": "MMGRole", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "MonsterTideDefend.js", "className": "MonsterTideDefend", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4"], "lifecycleMatches": ["生命周期1"], "totalScore": 7}, {"fileName": "MoreGamesItem.js", "className": "MoreGamesItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "MoreGamesView.js", "className": "MoreGamesView", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 10}, {"fileName": "MoveEntity.js", "className": "MoveEntity", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 6}, {"fileName": "MoveImg.js", "className": "MoveImg", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期3"], "totalScore": 10}, {"fileName": "MovingBGSprite.js", "className": "MovingBGSprite", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4"], "lifecycleMatches": ["生命周期5", "生命周期7"], "totalScore": 8}, {"fileName": "MTideDefendRmod.js", "className": "MTideDefendRmod", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征7"], "lifecycleMatches": [], "totalScore": 12}, {"fileName": "MTKRole.js", "className": "MTKRole", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4"], "lifecycleMatches": [], "totalScore": 6}, {"fileName": "NPC.js", "className": "NPC", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "OrganismBase.js", "className": "OrganismBase", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": ["生命周期1", "生命周期5"], "totalScore": 6}, {"fileName": "Pet.js", "className": "Pet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "RBadgePoint.js", "className": "RBadgePoint", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期7"], "totalScore": 14}, {"fileName": "ReflexBullet.js", "className": "ReflexBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "Role.js", "className": "Role", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "SelectAlert.js", "className": "Select<PERSON>lert", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征7"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "ShareButton.js", "className": "ShareButton", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期1", "生命周期2", "生命周期5", "生命周期6"], "totalScore": 18}, {"fileName": "SkeletonBullet.js", "className": "SkeletonBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": ["生命周期1", "生命周期5"], "totalScore": 10}, {"fileName": "TestItem.js", "className": "TestItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "ThrowBullet.js", "className": "ThrowBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "TornadoBullet.js", "className": "TornadoBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "TrackBullet.js", "className": "TrackBullet", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5"], "lifecycleMatches": [], "totalScore": 8}, {"fileName": "TrackItem.js", "className": "TrackItem", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征4", "支持特征6"], "lifecycleMatches": ["生命周期1", "生命周期3"], "totalScore": 10}, {"fileName": "VideoButton.js", "className": "VideoButton", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6", "支持特征7"], "lifecycleMatches": ["生命周期1", "生命周期2", "生命周期5", "生命周期6"], "totalScore": 18}, {"fileName": "VideoIcon.js", "className": "VideoIcon", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期5", "生命周期6"], "totalScore": 12}, {"fileName": "VisibleComponent.js", "className": "VisibleComponent", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征2", "支持特征3", "支持特征4", "支持特征5", "支持特征6"], "lifecycleMatches": ["生命周期1"], "totalScore": 13}, {"fileName": "WallBase.js", "className": "WallBase", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4"], "lifecycleMatches": [], "totalScore": 4}, {"fileName": "Weather.js", "className": "Weather", "requiredMatches": ["必需特征1", "必需特征2", "必需特征4"], "supportingMatches": ["支持特征1", "支持特征4", "支持特征7"], "lifecycleMatches": [], "totalScore": 6}], "falseCcclassFiles": [{"fileName": "Buff.js", "className": "Buff", "supportingMatches": [], "lifecycleMatches": ["生命周期1"], "totalScore": 1}, {"fileName": "BulletVoPool.js", "className": "BulletVoPool", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "Cfg.js", "className": "Cfg", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "FColliderManager.js", "className": "FColliderManager", "supportingMatches": ["支持特征7"], "lifecycleMatches": ["生命周期3"], "totalScore": 3}, {"fileName": "KnapsackVo.js", "className": "KnapsackVo", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "LatticeMap.js", "className": "LatticeMap", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "LevelMgr.js", "className": "Level", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MBackpackHero.js", "className": "MBPack", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MBRebound.js", "className": "MBRebound", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MCBossState.js", "className": "MCBossState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MChains.js", "className": "<PERSON><PERSON><PERSON>", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MMGuards.js", "className": "<PERSON><PERSON><PERSON><PERSON>", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MonstarTideDragon.js", "className": "MonstarTideDragon", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterElite.js", "className": "MonsterElite", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterState.js", "className": "MonsterState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MonsterTidal.js", "className": "MonsterTidal", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterTidalBoss.js", "className": "MonsterTidalBoss", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "MonsterTidalState.js", "className": "MonsterTidalState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MTideDefendRebound.js", "className": "MTideDefendRebound", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "MTKnife.js", "className": "MTKnife", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "PetState.js", "className": "PetState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "PropertyVo.js", "className": "Property", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}, {"fileName": "RBadgeModel.js", "className": "RBadgeModel", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "RewardEvent.js", "className": "RewardEvent", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "RoleState.js", "className": "RoleState", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "SkillManager.js", "className": "Skill", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "TaskModel.js", "className": "TaskModel", "supportingMatches": [], "lifecycleMatches": [], "totalScore": 0}, {"fileName": "TrackManger.js", "className": "TrackManger", "supportingMatches": [], "lifecycleMatches": ["生命周期7"], "totalScore": 1}, {"fileName": "Vehicle.js", "className": "Vehicle", "supportingMatches": ["支持特征1"], "lifecycleMatches": [], "totalScore": 2}], "errorFiles": []}