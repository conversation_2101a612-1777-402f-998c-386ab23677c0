var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MonsterTidalState = undefined;
var $2StateMachine = require("StateMachine");
var $2GameUtil = require("GameUtil");
var s = cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
(function (e) {
  var t = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t.direction = 1;
      t.distanceTraveledHorizontally = 0;
      t.range = 120;
      t.dtMoveX = .5;
      t.bAligning = false;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.MOVE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      var o;
      e.prototype.onEnter.call(this, t);
      t.playAction("move", true);
      var i = t.isInAttackRange();
      t.steering.resetFlag();
      if (1 == i) {
        t.steering.fleeOn();
      } else {
        t.steering.seekOn();
      }
      this.distanceTraveledHorizontally = 0;
      this.stepX = t.maxSpeed / 45;
      this.sceneW = $2GameUtil.GameUtil.getDesignSize.width / t.game.gameCamera.cutZoomRatio * .4;
      4 == t.monCfg.moveType && (null === (o = t.buffMgr) || undefined === o ? undefined : o.isHasID([9997])) && t.collider.setActive(false);
    };
    t.prototype.moveEnd = function () {
      if (this._owner.isBanMove || 0 == this._owner.maxSpeed) {
        return true;
      }
    };
    t.prototype.onUpdate = function (t, o) {
      if (!this._owner.isBanMove && (e.prototype.onUpdate.call(this, t, o), 0 != this._owner.maxSpeed)) {
        var i = t.steering.calculate();
        cc.Vec2.multiplyScalar(s, i, o);
        cc.Vec2.add(s, s, t.velocity);
        $2GameUtil.GameUtil.Vec2Truncate(s, t.maxSpeed);
        t.velocity = s;
        cc.Vec2.multiplyScalar(s, t.velocity, o);
        s.x *= this.dtMoveX;
        t.bAligning && (s.y = 100 * -o);
        t.sMoveRange && (this.range = t.sMoveRange);
        cc.Vec2.add(s, s, t.position);
        if (2 == t.monCfg.moveType) {
          var n = this.direction * o * 100;
          this.distanceTraveledHorizontally += n;
          s.x += n;
          if (Math.abs(this.distanceTraveledHorizontally) > this.range) {
            this.direction = 1 == this.direction ? -1 : 1;
            this.distanceTraveledHorizontally = 0;
          }
          s.x = cc.misc.clampf(s.x, -this.sceneW, this.sceneW);
        }
        t.enforceNonPeretration(s);
        t.setPosition(s);
        if (0 == t.isInAttackRange()) {
          if (t.readyToAttack()) {
            this._parentState.changeState($2StateMachine.State.Type.ATTACK);
          } else {
            this._parentState.changeState($2StateMachine.State.Type.IDLE);
          }
        }
      }
    };
    t.prototype.onExit = function (t) {
      var o;
      var i;
      var n;
      if (4 == t.monCfg.moveType && (null === (o = t.buffMgr) || undefined === o ? undefined : o.isHasID([9997]))) {
        null === (i = t.buffMgr) || undefined === i || i.clearBuffByID(9997);
        t.collider.setActive(true);
        null === (n = t.mySkeleton) || undefined === n || n.playQueue(["appear", "idle"], true);
      }
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.MoveState = t;
  var o = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.APPEAR;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("fiy", false);
      t.addBuff(9996);
      t.collider.setActive(false);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      this.time > 2 && this._parentState.changeState($2StateMachine.State.Type.IDLE);
    };
    t.prototype.onExit = function (t) {
      t.buffMgr.clearBuffByID(9996);
      t.collider.setActive(true);
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.AppearState = o;
  var i = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.MOVE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("move", true);
      var o = t.isInAttackRange();
      t.steering.resetFlag();
      if (1 == o) {
        t.steering.fleeOn();
      } else {
        t.steering.seekOn();
      }
    };
    t.prototype.onUpdate = function (t, o) {
      if (!this._owner.isBanMove && (e.prototype.onUpdate.call(this, t, o), 0 != this._owner.maxSpeed)) {
        var i = t.steering.calculate();
        cc.Vec2.multiplyScalar(s, i, o);
        cc.Vec2.add(s, s, t.velocity);
        $2GameUtil.GameUtil.Vec2Truncate(s, t.maxSpeed);
        t.velocity.set(s);
        cc.Vec2.multiplyScalar(s, t.velocity, o);
        s.y = 0;
        cc.Vec2.add(s, s, t.position);
        t.enforceNonPeretration(s);
        t.setPosition(s);
        if (t.velocity.magSqr() > 1e-5) {
          cc.Vec2.normalize(s, t.velocity);
          t.heading = s;
        }
        t.updateDir(o);
        if (0 == t.isInAttackRange()) {
          if (t.readyToAttack()) {
            this._parentState.changeState($2StateMachine.State.Type.ATTACK);
          } else {
            this._parentState.changeState($2StateMachine.State.Type.IDLE);
          }
        }
      }
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.MoveXState = i;
})(exports.MonsterTidalState || (exports.MonsterTidalState = {}));