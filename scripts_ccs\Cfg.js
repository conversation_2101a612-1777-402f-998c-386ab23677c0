var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Cfg = undefined;
var $2activityCfg = require("activityCfg");
var $2adRewardCfg = require("adRewardCfg");
var $2BagShopItemCfg = require("BagShopItemCfg");
var $2LvOutsideCfg = require("LvOutsideCfg");
var $2LvInsideCfg = require("LvInsideCfg");
var $2BulletEffectCfg = require("BulletEffectCfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2signCfg = require("signCfg");
var $2DropConfigCfg = require("DropConfigCfg");
var $2RoleLvCfg = require("RoleLvCfg");
var $2TowerLvCfg = require("TowerLvCfg");
var $2EquipLvCfg = require("EquipLvCfg");
var $2EquipMergeLvCfg = require("EquipMergeLvCfg");
var $2MiniGameEquipCfg = require("MiniGameEquipCfg");
var $2GameSettingCfg = require("GameSettingCfg");
var $2BagGuideCfg = require("BagGuideCfg");
var $2GuideCfg = require("GuideCfg");
var $2languageCfg = require("languageCfg");
var $2LevelExpCfg = require("LevelExpCfg");
var $2BoxLevelExpCfg = require("BoxLevelExpCfg");
var $2BagModeLvCfg = require("BagModeLvCfg");
var $2MiniGameLvCfg = require("MiniGameLvCfg");
var $2dragonPathCfg = require("dragonPathCfg");
var $2PayShopCfg = require("PayShopCfg");
var $2RoleUnlockCfg = require("RoleUnlockCfg");
var $2RoleCfg = require("RoleCfg");
var $2SoundCfg = require("SoundCfg");
var $2TaskCfg = require("TaskCfg");
var $2TaskTypeCfg = require("TaskTypeCfg");
var $2ProcessRewardsCfg = require("ProcessRewardsCfg");
var $2TowerCfg = require("TowerCfg");
var $2BagBuffCfg = require("BagBuffCfg");
var $2BuffCfg = require("BuffCfg");
var $2TowerCoinRewardCfg = require("TowerCoinRewardCfg");
var $2TowerAmethystRewardCfg = require("TowerAmethystRewardCfg");
var $2GameatrCfg = require("GameatrCfg");
var $2MapCfg = require("MapCfg");
var $2dmmRoleCfg = require("dmmRoleCfg");
var $2dmmItemCfg = require("dmmItemCfg");
var $2randomNameCfg = require("randomNameCfg");
var $2TowerMenuCfg = require("TowerMenuCfg");
var $2MonsterCfg = require("MonsterCfg");
var $2MonsterLvCfg = require("MonsterLvCfg");
var $2bagMonsterLvCfg = require("bagMonsterLvCfg");
var $2SkiilpoolCfg = require("SkiilpoolCfg");
var $2BuildModeSkiilpoolCfg = require("BuildModeSkiilpoolCfg");
var $2BagModeSkillPoolCfg = require("BagModeSkillPoolCfg");
var $2PoolListCfg = require("PoolListCfg");
var $2BagSkillCfg = require("BagSkillCfg");
var $2SkillCfg = require("SkillCfg");
var $2WeatherCfg = require("WeatherCfg");
var $2lzstring = require("lzstring");
var ie = function () {
  function e() {
    this._activity = new $2activityCfg.activityCfgReader();
    this._adReward = new $2adRewardCfg.adRewardCfgReader();
    this._BagShopItem = new $2BagShopItemCfg.BagShopItemCfgReader();
    this._LvOutside = new $2LvOutsideCfg.LvOutsideCfgReader();
    this._LvInside = new $2LvInsideCfg.LvInsideCfgReader();
    this._BulletEffect = new $2BulletEffectCfg.BulletEffectCfgReader();
    this._CurrencyConfig = new $2CurrencyConfigCfg.CurrencyConfigCfgReader();
    this._sign = new $2signCfg.signCfgReader();
    this._DropConfig = new $2DropConfigCfg.DropConfigCfgReader();
    this._RoleLv = new $2RoleLvCfg.RoleLvCfgReader();
    this._TowerLv = new $2TowerLvCfg.TowerLvCfgReader();
    this._EquipLv = new $2EquipLvCfg.EquipLvCfgReader();
    this._EquipMergeLv = new $2EquipMergeLvCfg.EquipMergeLvCfgReader();
    this._MiniGameEquip = new $2MiniGameEquipCfg.MiniGameEquipCfgReader();
    this._GameSetting = new $2GameSettingCfg.GameSettingCfgReader();
    this._BagGuide = new $2BagGuideCfg.BagGuideCfgReader();
    this._Guide = new $2GuideCfg.GuideCfgReader();
    this._language = new $2languageCfg.languageCfgReader();
    this._LevelExp = new $2LevelExpCfg.LevelExpCfgReader();
    this._BoxLevelExp = new $2BoxLevelExpCfg.BoxLevelExpCfgReader();
    this._BagModeLv = new $2BagModeLvCfg.BagModeLvCfgReader();
    this._MiniGameLv = new $2MiniGameLvCfg.MiniGameLvCfgReader();
    this._dragonPath = new $2dragonPathCfg.dragonPathCfgReader();
    this._PayShop = new $2PayShopCfg.PayShopCfgReader();
    this._RoleUnlock = new $2RoleUnlockCfg.RoleUnlockCfgReader();
    this._Role = new $2RoleCfg.RoleCfgReader();
    this._Sound = new $2SoundCfg.SoundCfgReader();
    this._Task = new $2TaskCfg.TaskCfgReader();
    this._TaskType = new $2TaskTypeCfg.TaskTypeCfgReader();
    this._ProcessRewards = new $2ProcessRewardsCfg.ProcessRewardsCfgReader();
    this._Tower = new $2TowerCfg.TowerCfgReader();
    this._BagBuff = new $2BagBuffCfg.BagBuffCfgReader();
    this._Buff = new $2BuffCfg.BuffCfgReader();
    this._TowerCoinReward = new $2TowerCoinRewardCfg.TowerCoinRewardCfgReader();
    this._TowerAmethystReward = new $2TowerAmethystRewardCfg.TowerAmethystRewardCfgReader();
    this._Gameatr = new $2GameatrCfg.GameatrCfgReader();
    this._Map = new $2MapCfg.MapCfgReader();
    this._dmmRole = new $2dmmRoleCfg.dmmRoleCfgReader();
    this._dmmItem = new $2dmmItemCfg.dmmItemCfgReader();
    this._randomName = new $2randomNameCfg.randomNameCfgReader();
    this._TowerMenu = new $2TowerMenuCfg.TowerMenuCfgReader();
    this._Monster = new $2MonsterCfg.MonsterCfgReader();
    this._MonsterLv = new $2MonsterLvCfg.MonsterLvCfgReader();
    this._bagMonsterLv = new $2bagMonsterLvCfg.bagMonsterLvCfgReader();
    this._Skiilpool = new $2SkiilpoolCfg.SkiilpoolCfgReader();
    this._BuildModeSkiilpool = new $2BuildModeSkiilpoolCfg.BuildModeSkiilpoolCfgReader();
    this._BagModeSkillPool = new $2BagModeSkillPoolCfg.BagModeSkillPoolCfgReader();
    this._PoolList = new $2PoolListCfg.PoolListCfgReader();
    this._BagSkill = new $2BagSkillCfg.BagSkillCfgReader();
    this._Skill = new $2SkillCfg.SkillCfgReader();
    this._Weather = new $2WeatherCfg.WeatherCfgReader();
    this.keyjs = {};
    this.keyJson = {
      activity: 1,
      adReward: 1,
      BagShopItem: 1,
      LvOutside: 1,
      LvInside: 1,
      BulletEffect: 1,
      CurrencyConfig: 1,
      sign: 1,
      DropConfig: 1,
      RoleLv: 1,
      TowerLv: 1,
      EquipLv: 1,
      EquipMergeLv: 1,
      MiniGameEquip: 1,
      GameSetting: 1,
      BagGuide: 1,
      Guide: 1,
      language: 1,
      LevelExp: 1,
      BoxLevelExp: 1,
      BagModeLv: 1,
      MiniGameLv: 1,
      dragonPath: 1,
      PayShop: 1,
      RoleUnlock: 1,
      Role: 1,
      Sound: 1,
      Task: 1,
      TaskType: 1,
      ProcessRewards: 1,
      Tower: 1,
      BagBuff: 1,
      Buff: 1,
      TowerCoinReward: 1,
      TowerAmethystReward: 1,
      Gameatr: 1,
      Map: 1,
      dmmRole: 1,
      dmmItem: 1,
      randomName: 1,
      TowerMenu: 1,
      Monster: 1,
      MonsterLv: 1,
      bagMonsterLv: 1,
      Skiilpool: 1,
      BuildModeSkiilpool: 1,
      BagModeSkillPool: 1,
      PoolList: 1,
      BagSkill: 1,
      Skill: 1,
      Weather: 1
    };
  }
  Object.defineProperty(e.prototype, "activity", {
    get: function () {
      return this._activity;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "adReward", {
    get: function () {
      return this._adReward;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagShopItem", {
    get: function () {
      return this._BagShopItem;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "LvOutside", {
    get: function () {
      return this._LvOutside;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "LvInside", {
    get: function () {
      return this._LvInside;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BulletEffect", {
    get: function () {
      return this._BulletEffect;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "CurrencyConfig", {
    get: function () {
      return this._CurrencyConfig;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "sign", {
    get: function () {
      return this._sign;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "DropConfig", {
    get: function () {
      return this._DropConfig;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "RoleLv", {
    get: function () {
      return this._RoleLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "TowerLv", {
    get: function () {
      return this._TowerLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "EquipLv", {
    get: function () {
      return this._EquipLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "EquipMergeLv", {
    get: function () {
      return this._EquipMergeLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "MiniGameEquip", {
    get: function () {
      return this._MiniGameEquip;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "GameSetting", {
    get: function () {
      return this._GameSetting;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagGuide", {
    get: function () {
      return this._BagGuide;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Guide", {
    get: function () {
      return this._Guide;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "language", {
    get: function () {
      return this._language;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "LevelExp", {
    get: function () {
      return this._LevelExp;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BoxLevelExp", {
    get: function () {
      return this._BoxLevelExp;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagModeLv", {
    get: function () {
      return this._BagModeLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "MiniGameLv", {
    get: function () {
      return this._MiniGameLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "dragonPath", {
    get: function () {
      return this._dragonPath;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "PayShop", {
    get: function () {
      return this._PayShop;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "RoleUnlock", {
    get: function () {
      return this._RoleUnlock;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Role", {
    get: function () {
      return this._Role;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Sound", {
    get: function () {
      return this._Sound;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Task", {
    get: function () {
      return this._Task;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "TaskType", {
    get: function () {
      return this._TaskType;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "ProcessRewards", {
    get: function () {
      return this._ProcessRewards;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Tower", {
    get: function () {
      return this._Tower;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagBuff", {
    get: function () {
      return this._BagBuff;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Buff", {
    get: function () {
      return this._Buff;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "TowerCoinReward", {
    get: function () {
      return this._TowerCoinReward;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "TowerAmethystReward", {
    get: function () {
      return this._TowerAmethystReward;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Gameatr", {
    get: function () {
      return this._Gameatr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Map", {
    get: function () {
      return this._Map;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "dmmRole", {
    get: function () {
      return this._dmmRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "dmmItem", {
    get: function () {
      return this._dmmItem;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "randomName", {
    get: function () {
      return this._randomName;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "TowerMenu", {
    get: function () {
      return this._TowerMenu;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Monster", {
    get: function () {
      return this._Monster;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "MonsterLv", {
    get: function () {
      return this._MonsterLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "bagMonsterLv", {
    get: function () {
      return this._bagMonsterLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Skiilpool", {
    get: function () {
      return this._Skiilpool;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BuildModeSkiilpool", {
    get: function () {
      return this._BuildModeSkiilpool;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagModeSkillPool", {
    get: function () {
      return this._BagModeSkillPool;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "PoolList", {
    get: function () {
      return this._PoolList;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "BagSkill", {
    get: function () {
      return this._BagSkill;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Skill", {
    get: function () {
      return this._Skill;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Weather", {
    get: function () {
      return this._Weather;
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.initBySingleJson = function () {
    return cc__awaiter(this, undefined, undefined, function () {
      var e = this;
      return cc__generator(this, function () {
        return [2, new Promise(function (t, o) {
          cc.resources.loadDir("config", function (e, i) {
            if (e) {
              cc.error("Cfg.initBySingleJson error", e);
              return void o();
            }
            for (var n = 0; n < i.length; n++) {
              var r = i[n];
              var a = r.name;
              if (this.hasOwnProperty("_" + a)) {
                this["_" + a].initByMap(r.json);
              } else {
                cc.warn("Cfg.initBySingleJson null, " + a);
              }
            }
            t(null);
          }.bind(e));
        })];
      });
    });
  };
  e.prototype.initRemoteJson = function (t, o, r) {
    return cc__awaiter(this, undefined, undefined, function () {
      var i;
      var a;
      var s;
      var c;
      return cc__generator(this, function () {
        i = this;
        a = t + ".json";
        s = t.split("/");
        c = s[s.length - 1];
        return [2, new Promise(function (t, n) {
          var s = new XMLHttpRequest();
          s.open("GET", a, true);
          s.responseType = "text";
          s.onreadystatechange = function () {
            if (4 == s.readyState) {
              if (200 == s.status) {
                var a = JSON.parse(s.responseText);
                if (!i.hasOwnProperty("_" + c)) {
                  cc.warn("Cfg.initRemoteJson null, " + c);
                  n("err");
                }
                i["_" + c].initByMap(a);
                e.cfgLoadNum += 1;
                o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
                r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
                t(null);
              } else {
                cc.resources.load("config/" + c, cc.JsonAsset, function (a, s) {
                  a && t("err");
                  var c = s.name;
                  if (!i.hasOwnProperty("_" + c)) {
                    cc.warn("Cfg.initRemoteJson null, " + c);
                    n("err");
                  }
                  i["_" + c].initByMap(s.json);
                  e.cfgLoadNum += 1;
                  o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
                  r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
                  t(null);
                });
              }
            }
          };
          s.setRequestHeader("content-type", "application/json");
          s.timeout = 5e3;
          s.ontimeout = function () {
            cc.resources.load("config/" + c, cc.JsonAsset, function (a, s) {
              a && t("err");
              var c = s.name;
              if (!i.hasOwnProperty("_" + c)) {
                cc.warn("Cfg.initRemoteJson null, " + c);
                n("err");
              }
              i["_" + c].initByMap(s.json);
              e.cfgLoadNum += 1;
              o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
              r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
              t(null);
            });
          };
          s.send();
        })];
      });
    });
  };
  e.prototype.initLocalJson = function (t, o, r) {
    return cc__awaiter(this, undefined, undefined, function () {
      var i;
      return cc__generator(this, function () {
        i = this;
        return [2, new Promise(function (n, a) {
          cc.resources.load("config/" + t, cc.JsonAsset, function (t, s) {
            t && a("err");
            var c = s.name;
            if (!i.hasOwnProperty("_" + c)) {
              cc.warn("Cfg.initRemoteJson null, " + c);
              a("err");
            }
            i["_" + c].initByMap(s.json);
            e.cfgLoadNum += 1;
            o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
            r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
            n(null);
          });
        })];
      });
    });
  };
  e.prototype.initRemoteConfig = function (t, o, r) {
    return cc__awaiter(this, undefined, undefined, function () {
      var i;
      var a;
      var s;
      var c;
      return cc__generator(this, function () {
        i = this;
        a = t + ".config";
        s = t.split("/");
        c = s[s.length - 1];
        return [2, new Promise(function (t, n) {
          var s = new XMLHttpRequest();
          s.open("GET", a, true);
          s.responseType = "text";
          s.onreadystatechange = function () {
            if (4 == s.readyState) {
              if (200 == s.status) {
                var a = JSON.parse($2lzstring.decompressFromBase64(s.responseText));
                if (!i.hasOwnProperty("_" + c)) {
                  cc.warn("Cfg.initRemoteJson null, " + c);
                  n("err");
                }
                i["_" + c].initByMap(a);
                e.cfgLoadNum += 1;
                o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
                r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
                t(null);
              } else {
                cc.resources.load("config/" + c, cc.Asset, function (a, s) {
                  a && n(a);
                  var l = c;
                  var u = s;
                  var p = JSON.parse($2lzstring.decompressFromBase64(u._nativeAsset));
                  if (!i.hasOwnProperty("_" + l)) {
                    cc.warn("Cfg.initRemoteConfig null, " + l);
                    n("err");
                  }
                  i["_" + l].initByMap(p);
                  e.cfgLoadNum += 1;
                  o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
                  r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
                  t(null);
                });
              }
            }
          };
          s.setRequestHeader("content-type", "text/plain");
          s.timeout = 5e3;
          s.ontimeout = function () {
            cc.resources.load("config/" + c, cc.Asset, function (a, s) {
              a && n(a);
              var l = c;
              var u = s;
              var p = JSON.parse($2lzstring.decompressFromBase64(u._nativeAsset));
              cc.log(p);
              if (!i.hasOwnProperty("_" + l)) {
                cc.warn("Cfg.initRemoteConfig null, " + l);
                n("err");
              }
              i["_" + l].initByMap(p);
              e.cfgLoadNum += 1;
              o && (o.string = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) + "%");
              r && (r.progress = (e.cfgLoadNum > 10 ? 10 : e.cfgLoadNum) / 100);
              t(null);
            });
          };
          s.send();
        })];
      });
    });
  };
  e.prototype.HasTag = function (e, t) {
    return null != e.tags && e.tags.indexOf(t) >= 0;
  };
  e.prototype.selectArray = function (e, t, o, i) {
    var n = e[t];
    if (null == n) {
      return i;
    } else {
      return n[o] || i;
    }
  };
  e.cfgLoadNum = 0;
  return e;
}();
exports.Cfg = new ie();