var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_ReflexBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.border = {
      left: null,
      right: null,
      top: null,
      bottom: null
    };
    t.reflexNum = 0;
    t.checkTick = 0;
    t._cameraSize = $2GameUtil.GameUtil.getDesignSize;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    var o = {
      width: this._cameraSize.width / this.gameCamera.cutZoomRatio * .5,
      height: this._cameraSize.height / this.gameCamera.cutZoomRatio * .5
    };
    this.border.left = -o.width;
    this.border.right = o.width;
    this.border.top = o.height;
    this.border.bottom = -o.height;
    this.reflexNum = 0;
  };
  Object.defineProperty(_ctor.prototype, "gameCamera", {
    get: function () {
      return this.game.gameCamera;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    var o = this.checkObjectOutOfBounds(t);
    if (o && o.isout) {
      if (this._vo.belongSkill.cutVo.reflexCount == this.reflexNum) {
        return void (this.vo.lifeTime = 0);
      }
      this.reflexNum++;
      this._vo.shootDir = this.computeReflectionVector(this._vo.shootDir, o.normalVector);
    }
    this.updateDir(t);
  };
  _ctor.prototype.checkObjectOutOfBounds = function (e) {
    if ((this.checkTick += e) > .1) {
      var t = {
        width: this._cameraSize.width / this.gameCamera.cutZoomRatio * .5,
        height: this._cameraSize.height / this.gameCamera.cutZoomRatio * .5
      };
      var o = {
        isout: false,
        normalVector: cc.Vec2.ZERO
      };
      o.isout = false;
      var i = this.node.position.sub(this.gameCamera.position);
      if (!(Math.abs(i.x) < t.width && Math.abs(i.y) < t.height)) {
        o.isout = true;
        if (this.node.x < this.border.left) {
          o.normalVector = cc.v2(1, 0);
        } else if (this.node.x > this.border.right) {
          o.normalVector = cc.v2(-1, 0);
        } else if (this.node.y < this.border.bottom) {
          o.normalVector = cc.v2(0, 1);
        } else {
          o.normalVector = cc.v2(0, -1);
        }
        this.checkTick = 0;
      }
      return o;
    }
  };
  _ctor.prototype.computeReflectionVector = function (e, t) {
    var o = cc.Vec2.dot(e, t);
    return e.sub(t.mul(2 * o));
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/ReflexBullet")], _ctor);
}($2Bullet.default);
exports.default = def_ReflexBullet;