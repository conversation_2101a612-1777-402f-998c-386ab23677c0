var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
var def_Bullet_FollowTarget = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.curTargetPos = cc.v2();
    t.curTargetDir = cc.v2();
    t.randomTime = 1;
    t._deltaTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this.changeFollow();
  };
  _ctor.prototype.changeFollow = function () {
    if (!(this._deltaTime < this.randomTime)) {
      this.curTarget = $2GameUtil.GameUtil.randomArr(this.game.getTarget({
        pos: this.position,
        radius: this.vo.skillCfg.dis,
        ignoreID: 1 == this.game.monsterMap.size ? [] : [this.idIgnore],
        targetCamp: this.vo.atkCamp,
        target: this.vo.ower
      }));
      if (this.curTarget) {
        this.idIgnore = this.curTarget.ID, this.curTargetPos.set(this.curTarget.position), this.t = 0;
      }
      this._deltaTime = 0;
    }
  };
  _ctor.prototype.onUpdate = function (t) {
    var o;
    e.prototype.onUpdate.call(this, t);
    if (this.isActive) {
      if (null === (o = this.curTarget) || undefined === o ? undefined : o.isActive) {
        this.curTargetPos.set(this.curTarget.bodyPosition), cc.Vec2.squaredDistance(this.position, this.curTargetPos) > Math.pow(100, 2) && this.curTargetDir.set(this.curTargetPos.sub(this.position).normalize()), this.t += .3 * t, this.t = cc.misc.clamp01(this.t), cc.Vec2.lerp(p, this.vo.shootDir, this.curTargetDir, this.t), this.vo.shootDir.addSelf(p).normalizeSelf();
      } else {
        this._deltaTime > this.randomTime && this.changeFollow();
      }
      this._deltaTime += t;
      this.updateDir(0);
      cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t);
      cc.Vec2.add(p, this.position, p);
      this.setPosition(p);
    }
  };
  _ctor.prototype.setHurt = function (t) {
    e.prototype.setHurt.call(this, t);
    this.changeFollow();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_FollowTarget")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_FollowTarget;