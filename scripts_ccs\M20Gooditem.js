var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Manager = require("Manager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20Gooditem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.Count = null;
    t.goodsbg = null;
    t.goodimg = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setdata = function (e) {
    var t;
    this.Count.node.setActive(e.count);
    this.Count.string = null !== (t = e.count) && undefined !== t ? t : 0;
    $2Manager.Manager.loader.loadSpriteToSprit(e.path, this.goodimg, this.node.parent);
    if (!e.bgpath) {
      var img = this.node.getChildByName("img").getComponent(cc.Sprite);
      img.sizeMode = cc.Sprite.SizeMode.RAW;
    }
    this.node.getChildByName("icon_pintu").active = e.isfrag;
    this.goodsbg.node.active = e.bgpath;
    $2Manager.Manager.loader.loadSpriteToSprit(e.bgpath, this.goodsbg, this.node.parent);
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "Count", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "goodsbg", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "goodimg", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_M20Gooditem;