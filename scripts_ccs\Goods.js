var i;
var n;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2DropConfigCfg = require("DropConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2Game = require("Game");
var $2TrackManger = require("TrackManger");
var $2BaseEntity = require("BaseEntity");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var C = cc.v2();
(n = {})[$2DropConfigCfg.DropConfigDefine.AddHpGoods] = {
  trackType: $2TrackManger.TrackManger.Type.PropHp,
  icon: "v1/images/fight/icon/hpbottle"
};
n[$2DropConfigCfg.DropConfigDefine.CiTie] = {
  trackType: $2TrackManger.TrackManger.Type.PropMagnet,
  icon: "v1/images/fight/icon/magnet"
};
n[$2DropConfigCfg.DropConfigDefine.BabyBox] = {
  trackType: $2TrackManger.TrackManger.Type.PropBox,
  icon: "v1/images/fight/icon/box"
};
var w = n;
var def_Goods = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._entityType = $2BaseEntity.EntityType.Goods;
    t._countFrame = 0;
    t.isPickUp = false;
    t.canMove = true;
    t._checkTime = 1;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "target", {
    get: function () {
      this._target || (this.target = this.game.mainRole);
      return this._target;
    },
    set: function (e) {
      this._target = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "targetGap", {
    get: function () {
      var e = cc.Vec2.distance(this.target.position, this.position);
      this._checkTime = e / 100 * .1;
      return e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    cc.Tween.stopAllByTarget(this.node);
    this.node.setActive(true);
    this.isPickUp = false;
    this._checkTime = 1;
    this.icon.node.setPosition(cc.Vec2.ZERO);
    w[this.data.id] && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetTrack, cc__assign({
      node: this.node
    }, w[this.data.id]));
  };
  Object.defineProperty(_ctor.prototype, "data", {
    get: function () {
      return this._data;
    },
    set: function (e) {
      var t;
      var o;
      this._data = e;
      this.bg || (this.bg = this.node.getComByChild(cc.Sprite, "bg"));
      this.bg.spriteFrame = null;
      this.icon || (this.icon = this.node.getComByChild(cc.Sprite, "icon"));
      this.icon.spriteFrame = null;
      this.icon.node.removeAllChildren();
      if (e.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
        var i = $2Game.ModeCfg.Buff.get(e.param);
        $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(i.rarity).bubbleBg, this.icon, null === (t = this.game) || undefined === t ? undefined : t.gameNode);
        new cc.Node("msg").setAttribute({
          parent: this.icon.node
        }).addComponent(cc.RichText).setAttribute({
          string: i.desc,
          fontSize: 36,
          lineHeight: 38,
          maxWidth: 180,
          horizontalAlign: cc.macro.TextAlignment.CENTER
        });
      } else {
        $2Manager.Manager.loader.loadSpriteToSprit(e.icon || $2Cfg.Cfg.CurrencyConfig.get(e.id).icon, this.icon, null === (o = this.game) || undefined === o ? undefined : o.gameNode);
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (this.isActive && this.canMove) {
      if (!this.canMove) {
        return;
      }
      if (this.isPickUp) {
        if (this._countFrame-- < 0) {
          cc.Vec2.lerp(C, this.position, this.target.position, t + .2);
          this.setPosition(C);
          this.targetGap < this.target.Picking && this.getReward();
        } else {
          C = this.position.sub(this.target.position).normalize().mul(5);
          this.setPosition(this.position.add(C));
          this.icon.node.y += 5;
        }
      } else if ((this._checkTime -= t) <= 0 && this.targetGap < this.target.Picking) {
        this.isPickUp = true, this._countFrame = 10;
      }
    }
  };
  _ctor.prototype.getReward = function () {
    var e = this;
    if (this.data.type == $2GameSeting.GameSeting.GoodsType.Money) {
      this.game.knapsackMgr.add(this.data);
    } else if (this.data.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
      this.target.addBuff(this.data.param);
    } else if (this.data.type == $2GameSeting.GameSeting.GoodsType.BuffDropSelect) {
      var t = this.data;
      $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
        type: t.id,
        isBinding: true,
        getPool: function (o) {
          return e.game.mode.fightBuffWidth(t.id, o, $2Cfg.Cfg.PoolList.get(t.param).Pool);
        }
      }).setDailyTime(.3));
    }
    this.isDead = true;
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2BaseEntity.default);
exports.default = def_Goods;