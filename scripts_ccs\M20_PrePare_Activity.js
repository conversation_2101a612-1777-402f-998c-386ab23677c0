var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2ModeChainsModel = require("ModeChainsModel");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_PrePare_Activity = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    this.nodeArr[0].getChildByName("MoreGames").zIndex = 999;
    this.nodeArr[1].active = 1 == $2Manager.Manager.vo.switchVo.GameKnife;
  };
  _ctor.prototype.onEnable = function () {
    this.resetState();
  };
  _ctor.prototype.resetState = function () {
    var e = this;
    this.unscheduleAllCallbacks();
    var t = 0;
    $2Cfg.Cfg.activity.forEach(function (o) {
      e.setItem(e.mode.rVo.activityList[o.id], t);
      t++;
    });
  };
  _ctor.prototype.setItem = function (e, t) {
    var o = $2Cfg.Cfg.activity.get(e.id);
    var i = this.nodeArr[0].filterChild({
      name: "item"
    })[t] || cc.instantiate(this.nodeArr[0].children[0]).setAttribute({
      parent: this.nodeArr[0]
    });
    i.getComByPath(cc.Label, "name").string = o.actName;
    i.zIndex = o.unlockChapter;
    var n = i.getChildByName("btn");
    n.targetOff(this);
    n.on(cc.Node.EventType.TOUCH_END, function () {
      if ($2ModeBackpackHeroModel.default.instance.userEquipPack.filter(function (e) {
        return e.isFitOut;
      }).length < 8) {
        return $2AlertManager.AlertManager.showNormalTips("装备数量不足");
      } else {
        if (0 == e.cgNum) {
          return $2AlertManager.AlertManager.showNormalTips("挑战次数不足");
        } else {
          return void $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
            type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
            val: $2Manager.Manager.vo.switchVo.fightStamina,
            call: function (t) {
              if (t == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
                e.cgNum--;
                var i = $2Game.Game.getMouth($2Game.Game.Mode.CHAINS);
                $2Notifier.Notifier.send(i.mouth, $2Game.Game.Mode.CHAINS, $2MVC.MVC.openArgs().setParam({
                  id: o.lvId
                }));
              }
            }
          });
        }
      }
    }, this);
    $2Manager.Manager.loader.loadSpriteToSprit(o.actIcon, i.getComponent(cc.Sprite));
    var r = i.getChildByName("rewardList");
    o.reward.forEach(function (e, t) {
      var o = $2Cfg.Cfg.CurrencyConfig.get(e);
      var i = r.children[t] || cc.instantiate(r.children[0]).setAttribute({
        parent: r
      });
      $2Manager.Manager.loader.loadSpriteToSprit(o.icon, i.getComByChild(cc.Sprite, "icon"));
      $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(o.rarity).blockImg, i.getComponent(cc.Sprite));
    });
    r.spliceNode(o.reward.length, 10);
    var p = function () {
      i.getComByChild(cc.Label, "time").string = cc.js.formatStr("剩余时间:%s", $2GameUtil.GameUtil.formatSeconds(($2Time.Time.midnight - $2Time.Time.serverTimeMs) / 1e3).str);
      i.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("通关第%d章解锁", o.unlockChapter);
      i.getComByChild(cc.Label, "num").string = cc.js.formatStr("今日剩余挑战次数:%d", e.cgNum);
      i.getChildByName("mask").setActive($2Manager.Manager.leveMgr.vo.curPassLv < o.unlockChapter);
    };
    p();
    this.schedule(p, 1);
  };
  _ctor.prototype.onShowFinish = function () {
    var e;
    var t;
    null === (t = null === (e = this.param) || undefined === e ? undefined : e.showCb) || undefined === t || t.call(e, this.node);
    this.node.opacity = 255;
  };
  _ctor.prototype.onOpen = function () {
    this.node.opacity = 0;
  };
  _ctor.prototype.getThis = function () {
    return this;
  };
  _ctor.prototype.onBtn = function (e, t) {
    if (t.includes("ui/")) {
      var o = $2MVC.MVC.openArgs();
      if ("ui/setting/MoreGamesView" == t) {
        var i = $2MVC.MVC.openArgs();
        i.setParam({
          pageIndex: $2Game.Game.Mode.CHAINS
        });
        return void $2UIManager.UIManager.Open("ui/setting/MoreGamesView", i);
      }
      $2UIManager.UIManager.OpenInQueue(t, o);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_PrePare_Activity"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_PrePare_Activity;