var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2FCollider = require("FCollider");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_FBoxCollider = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.worldPoints = [cc.v2(), cc.v2(), cc.v2(), cc.v2()];
    t.worldEdge = [];
    t.isConvex = true;
    t._size = cc.size(100, 100);
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "type", {
    get: function () {
      return $2FCollider.ColliderType.Box;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "size", {
    get: function () {
      return this._size;
    },
    set: function (e) {
      this._size.width = e.width < 0 ? 0 : e.width;
      this._size.height = e.height < 0 ? 0 : e.height;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setSize = function (e) {
    this.size = e;
  };
  cc__decorate([ccp_property(cc.Size)], _ctor.prototype, "_size", undefined);
  cc__decorate([ccp_property], _ctor.prototype, "size", null);
  return cc__decorate([ccp_ccclass, ccp_menu("碰撞组件Ex/FBoxCollider")], _ctor);
}($2FCollider.default);
exports.default = def_FBoxCollider;