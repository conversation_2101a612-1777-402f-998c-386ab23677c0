// Bullet_RigidBody.ts
// 从 Bullet_RigidBody.js 转换而来

// 子弹类
export class Bullet_RigidBody {
    private position: cc.Vec2 = cc.Vec2.ZERO;
    private velocity: cc.Vec2 = cc.Vec2.ZERO;
    private damage: number = 0;

    constructor() {
        this.init();
    }

    private init(): void {
        // TODO: 初始化子弹
    }

    update(dt: number): void {
        // TODO: 更新子弹位置
    }

    // TODO: 添加子弹方法
}