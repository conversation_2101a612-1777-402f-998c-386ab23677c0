var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20Equipitem = require("M20Equipitem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_Pop_EquipInfo = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.equipitem = null;
    t.equipNode = null;
    t.equipSpec1 = null;
    t.equipSpec1Node = null;
    t.equipSpec2 = null;
    t.equipSpec2Node = null;
    t.btnUpgrade = null;
    t.cost = null;
    t.btnUpgradeLabel = null;
    t.levelup = null;
    t.lockTips = null;
    t.curlvcfg = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.onOpen = function () {
    this.refresh();
  };
  _ctor.prototype.refresh = function () {
    var e;
    var t = this;
    this.equipNode.destroyAllChildren();
    var o = this.param._param.equipid;
    var i = $2Cfg.Cfg.EquipLv.filter({
      equipId: o
    });
    var n = this.mode.getEquipLv(o);
    this.curlvcfg = i.find(function (e) {
      return e.lv == n;
    });
    var r = (null === (e = this.mode.userEquipPack.getItem(o)) || undefined === e ? undefined : e.lv) || 0;
    var a = i[i.length - 1];
    var c = this.curlvcfg.lv >= a.lv;
    this.btnUpgrade.active = null != this.mode.getEquip(this.param._param.equipid);
    this.lockTips.string = this.mode.getEquip(this.param._param.equipid) ? "" : this.getLockDesc(o);
    this.btnUpgradeLabel.string = c ? "已满级" : "升级";
    this.cost.node.parent.active = !c;
    this.cost.string = c ? "" : i.find(function (e) {
      return e.lv == n;
    }).upgradeCost;
    this.btnUpgrade.getComponent(cc.Button).interactable = !c;
    var u = cc.instantiate(this.equipitem);
    u.setParent(this.equipNode);
    u.setPosition(0, 0);
    u.getComponent($2M20Equipitem.default).setInfo(o);
    this.curlvcfg.baseAtr && this.curlvcfg.baseAtr.forEach(function (e, o) {
      var i = $2Cfg.Cfg.Gameatr.get(e[0]);
      var n = e[1];
      var r = e[2];
      var a = t.equipSpec1Node.children[o] || cc.instantiate(t.equipSpec1Node.children[0]);
      a.setAttribute({
        parent: t.equipSpec1Node
      });
      $2Manager.Manager.loader.loadSpriteToSprit(i.icon, a.getComByChild(cc.Sprite, "icon"));
      a.getComByChild(cc.Label, "atrname").string = i.name;
      if (2e3 == i.id) {
        a.getComByChild(cc.RichText, "val").text = cc.js.formatStr("<outline color=black width=3>%s</outline>", $2GameSeting.GameSeting.getSelectVal(n).name);
      } else {
        a.getComByChild(cc.RichText, "val").text = cc.js.formatStr("<outline color=black width=3>" + n + " " + (r ? "<color=#00ff00>+" + r + "</c>" : "") + (i.unit || "") + "</outline>");
      }
    });
    this.equipSpec2Node.children.forEach(function (e) {
      return e.active = false;
    });
    var p = $2Cfg.Cfg.RoleUnlock.get(o);
    p.rarity;
    $2Manager.Manager.vo.switchVo.equip3SelectShow[p.rarity];
    $2Cfg.Cfg.EquipMergeLv.filter({
      equipId: o,
      lv: 4
    }).length;
    i.forEach(function (e, o) {
      if (e.unlockBuff) {
        var i = $2Cfg.Cfg.Buff.get(e.unlockBuff);
        if (i) {
          var n = t.equipSpec2Node.children[o] || cc.instantiate(t.equipSpec2Node.children[0]);
          n.setAttribute({
            parent: t.equipSpec2Node,
            position: cc.Vec2.ZERO,
            active: true
          });
          n.getComByChild(cc.RichText, "RichText").text = cc.js.formatStr("<outline color=black width=2>Lv.%d</outline>%s", e.lv, i.desc);
          n.getComByChild(cc.Label, "name").string = i.name;
          var a = $2GameSeting.GameSeting.getRarity(t.mode.buffmap[i.rarity]).blockImg;
          $2Manager.Manager.loader.loadSpriteToSprit(a, n.getComByPath(cc.Sprite, "bg_icon_01"));
          $2Manager.Manager.loader.loadSpriteToSprit(i.icon, n.getComByPath(cc.Sprite, "bg_icon_01/icon"));
          n.getChildByName("mask").setActive(r < e.lv);
          n.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("%d级解锁", e.lv);
        }
      }
    });
  };
  _ctor.prototype.getLockDesc = function (e) {
    var t = $2Cfg.Cfg.RoleUnlock.find({
      id: e
    });
    if (t) {
      if (1 == t.unlock) {
        return cc.js.formatStr("通关章节%d解锁", t.Count);
      }
      if (2 == t.unlock) {
        return cc.js.formatStr("%d个碎片解锁", t.Count);
      }
      if (3 == t.unlock) {
        return cc.js.formatStr("%d个视频解锁", t.Count);
      }
    }
    return "";
  };
  _ctor.prototype.upgrade = function () {
    var e = this;
    13 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
    if (!(this.curlvcfg.lv >= $2Cfg.Cfg.EquipLv.filter({
      equipId: this.curlvcfg.equipId
    }).length)) {
      var t = this.mode.fragmentsPack.getVal(this.curlvcfg.equipId) >= this.curlvcfg.upgradeNeedle;
      var o = this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);
      if (t) {
        if (o) {
          this.mode.fragmentsPack.useUp(this.curlvcfg.equipId, this.curlvcfg.upgradeNeedle);
          $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);
          this.levelup.node.setActive(true);
          this.levelup.clearTracks();
          this.levelup.setAnimation(1, "animation", false);
          this.scheduleOnce(function () {
            e.levelup.node.setActive(false);
          }, 1);
          this.mode.userEquipPack.upgrade(this.param._param.equipid);
          this.refresh();
          $2Notifier.Notifier.send($2ListenID.ListenID.M20_EquipUpgrade, this.param._param.equipid);
          $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 9);
          $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Upgrade", {
            UpgradeEquipId: this.curlvcfg.equipId,
            UpgradeEquipLv: this.curlvcfg.lv
          });
        } else {
          $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
            currencyType: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
          }));
        }
      } else {
        $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
          currencyType: this.curlvcfg.equipId
        }));
      }
    }
  };
  _ctor.prototype.onClose = function () {
    if (14 == $2Manager.Manager.vo.userVo.guideIndex) {
      var e = $2Notifier.Notifier.call($2CallID.CallID.M20_SelectEquip);
      e.node.getComByPath(cc.ScrollView, "bg/bottom").scrollTo(e.notUnlockBox.parent.children[0].position);
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: e.notUnlockBox.parent,
        enableclick: true,
        blockevent: true
      });
    }
  };
  _ctor.prototype.setInfo = function () {
    if (12 == $2Manager.Manager.vo.userVo.guideIndex) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: this.btnUpgrade
      });
    }
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.Prefab)], _ctor.prototype, "equipitem", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipNode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipSpec1", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipSpec1Node", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipSpec2", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipSpec2Node", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnUpgrade", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "cost", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "btnUpgradeLabel", undefined);
  cc__decorate([ccp_property(sp.Skeleton)], _ctor.prototype, "levelup", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "lockTips", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_EquipInfo"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_EquipInfo;