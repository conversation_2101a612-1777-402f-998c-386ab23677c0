// FColliderManager.ts
// 从 FColliderManager.js 转换而来

// 管理器类
export class FColliderManager {
    private static instance: FColliderManager;

    public static getInstance(): FColliderManager {
        if (!this.instance) {
            this.instance = new FColliderManager();
        }
        return this.instance;
    }

    private constructor() {
        this.init();
    }

    private init(): void {
        // TODO: 初始化管理器
    }

    // TODO: 添加管理器方法
}