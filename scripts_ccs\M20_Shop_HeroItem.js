var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20_Shop_HeroItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.goodname = null;
    t.oncecost = null;
    t.weekcost = null;
    t._canclick = true;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setdata = function (e) {
    this.oncecost.string = e.costVal[1];
    this.weekcost.string = e.costVal[2];
  };
  _ctor.prototype.getfrag = function (e, t) {
    var o = this;
    if (this._canclick) {
      this._canclick = false;
      this.scheduleOnce(function () {
        o._canclick = true;
      }, 1);
      t = Number(t);
      for (var i = 0; i < t; i++) {
        var n = Math.random() > .5 ? "A" : "B";
        var r = "A" == n ? $2Manager.Manager.vo.switchVo.heroAfragmentWeight : $2Manager.Manager.vo.switchVo.heroBfragmentWeight;
        var a = $2GameUtil.GameUtil.getRandomByWeightInArray(r, 1)[0];
        var s = $2Cfg.Cfg.RoleUnlock.find({
          type: 3,
          rarity: n
        });
        var c = $2Cfg.Cfg.RoleLv.find({
          roleId: s.id
        });
        var p = [{
          id: s.id,
          num: a,
          type: c.type,
          rarity: $2Cfg.Cfg.CurrencyConfig.get(c.type).rarity
        }];
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GameRewardView", $2MVC.MVC.openArgs().setParam({
          data: p
        }));
        this.mode.addFragment(c.roleId, a);
        this.mode.dailyAdpack.addGoods("fragadcount" + c.roleId);
      }
    }
  };
  _ctor.prototype.sendEvent = function (e, t) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign({
      Type: e,
      Scene: t
    }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
  };
  _ctor.prototype.onDestroy = function () {};
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "goodname", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "oncecost", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "weekcost", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_M20_Shop_HeroItem;