var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_VideoButton = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.clickEvents = [];
    t.viewScene = "";
    t.isAm = true;
    t.desc = "";
    t.interactable = true;
    t._scale = 1;
    t.isADcoupons = false;
    t.isClick = false;
    t.isCanClick = true;
    t._isForFree = false;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "scene", {
    get: function () {
      var e;
      var t;
      this._scene || (this._scene = this.viewScene || (null === (e = this.baseView) || undefined === e ? undefined : e.eventScene) || (null === (t = this.node) || undefined === t ? undefined : t.name) || "notDefine");
      return this._scene;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    this._scale = this.node.scale;
    this._baseView = this.baseView;
    this.node.getORaddComponent(cc.BlockInputEvents);
    this.videoIcon = this.node.getChildByName("videoicon");
  };
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
    this.resetState();
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.start = function () {
    this.sendEvent("show");
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.resetState = function () {
    var e;
    var t = (null === (e = this.game) || undefined === e ? undefined : e.adcoupons) || 0;
    this.isADcoupons = t > 0;
  };
  _ctor.prototype.changeListener = function (e) {
    this.node.changeListener(e, cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
    this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
  };
  _ctor.prototype.onTouchStart = function () {
    var e = this;
    if (this.interactable) {
      $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.button_click);
      this.isClick = true;
      cc.Tween.stopAllByTarget(this.node);
      cc.tween(this.node).to(.1, {
        scale: .9 * this._scale
      }).start();
      this.scheduleOnce(function () {
        e.isClick = false;
      }, 1);
    }
  };
  _ctor.prototype.onTouchEnd = function (e) {
    var t = this;
    if (this.interactable) {
      if (this.isClick) {
        var o = wonderSdk.isGoogleAndroid || wonderSdk.isIOS;
        if (!$2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) && o && this.desc) {
          $2AlertManager.AlertManager.showSelectAlert({
            title: "提示",
            desc: this.desc,
            confirmText: "确认",
            isVideo: true,
            showShop: true,
            confirm: function () {
              cc.log("确认");
              t.loadVideo(e);
            }
          });
        } else {
          this.loadVideo(e);
        }
      }
      this.repeatForeverAm();
    }
  };
  _ctor.prototype.loadVideo = function (e) {
    var t = this;
    if (this.isCanClick) {
      this.sendEvent("click");
      if (this.isForFree) {
        this.isForFree = false;
        for (var o = 0; o < this.clickEvents.length; o++) {
          var i = this.clickEvents[o];
          i.emit([e, i.customEventData]);
        }
      } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (o) {
          if (o == wonderSdk.VideoAdCode.COMPLETE) {
            t.sendEvent("success");
            for (var i = 0; i < t.clickEvents.length; i++) {
              var n = t.clickEvents[i];
              n.emit([e, n.customEventData]);
            }
          }
        });
        $2Time.Time.delay(2, function () {
          $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        });
      }
    }
  };
  Object.defineProperty(_ctor.prototype, "isForFree", {
    get: function () {
      return this._isForFree;
    },
    set: function (e) {
      this._isForFree = e;
      // $2Manager.Manager.loader.loadSpriteToSprit("img/ui/" + (e ? "icon_tltq_sx0" : "icon_ads"), this.videoIcon.getComponent(cc.Sprite));
      $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/" + (e ? "sp_refresh" : "sp_ad"), this.videoIcon.getComponent(cc.Sprite));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setCanClick = function (e) {
    this.isCanClick = e;
  };
  _ctor.prototype.repeatForeverAm = function () {
    var e = this;
    cc.Tween.stopAllByTarget(this.node);
    cc.tween(this.node).to(.1, {
      scale: this._scale
    }).call(function () {
      e.isCanClick && e.isAm && cc.tween(e.node).sequence(cc.tween().to(.3, {
        scale: e._scale + .1
      }, {
        easing: "sineInOut"
      }), cc.tween().to(.3, {
        scale: e._scale
      }, {
        easing: "sineInOut"
      })).repeatForever().start();
    }).start();
  };
  _ctor.prototype.sendEvent = function (e) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign(cc__assign({
      Type: e,
      Scene: this.scene,
      ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
    }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}), this.eventPram || {}));
  };
  Object.defineProperty(_ctor.prototype, "baseView", {
    get: function () {
      var e;
      for (var t = this.node; !e && t && t.parent;) {
        var o = t.parent.getComponent($2MVC.MVC.BaseView);
        if (o) {
          e = o;
        } else {
          t.parent && (t = t.parent);
        }
      }
      return e;
    },
    enumerable: false,
    configurable: true
  });
  cc__decorate([ccp_property([cc.Component.EventHandler])], _ctor.prototype, "clickEvents", undefined);
  cc__decorate([ccp_property({
    displayName: "广告场景(默认界面名称)"
  })], _ctor.prototype, "viewScene", undefined);
  cc__decorate([ccp_property({
    displayName: "是否有动画"
  })], _ctor.prototype, "isAm", undefined);
  cc__decorate([ccp_property({
    displayName: "广告提示描述"
  })], _ctor.prototype, "desc", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/VideoButton")], _ctor);
}(cc.Component);
exports.default = def_VideoButton;