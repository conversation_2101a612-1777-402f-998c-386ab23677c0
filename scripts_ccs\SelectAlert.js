var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2GameSeting = require("GameSeting");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_SelectAlert = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.reasonDesc = null;
    t.wayDesc = null;
    t.confirmText = null;
    t.cancelText = null;
    t.videoIcon = null;
    t.icon = null;
    t.cbConfirm = null;
    t.cbCancel = null;
    t.cbClickConfirm = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onOpen = function () {
    var t = this;
    e.prototype.onOpen.call(this);
    var o = this.args = this._openArgs.param;
    this.cbConfirm = o && o.confirm;
    this.cbCancel = o && o.cancel;
    this.reasonDesc.text = cc.js.formatStr("<outline color=#474747 width=3>%s</outline>", o && o.desc || "");
    this.reasonDesc.node.setActive(!!o.desc);
    this.wayDesc.string = o && o.title || "提示";
    this.confirmText.string = o && o.confirmText || "确 认";
    this.cancelText.string = o && o.cancelText || "取 消";
    this.cbClickConfirm = o && o.clickconfirm;
    this.UItype = o.tag ? o.tag : $2GameSeting.GameSeting.TweenType.Not;
    this.videoIcon.setActive(o.isVideo);
    this.icon.node.setActive(!!o.icon);
    if (o.icon) {
      $2Manager.Manager.loader.loadSpriteToSprit(o.icon, this.icon);
      this.icon.node.scale = o.iconScale || 2;
    }
    this.node.getComByPath(cc.Layout, "bg/content/box").setAttribute({
      paddingTop: o.icon ? 20 : 100,
      paddingBottom: o.icon ? 20 : 100
    });
    cc.find("bg/ToggleBox", this.node).setActive(o.hasIgnore);
    wonderSdk.hasPay && o.showShop && !$2Manager.Manager.Shop.checkOrder(100) && $2Manager.Manager.loader.loadPrefab("ui/shop/Shop_QuickBuy", this.node).then(function (e) {
      e.setAttribute({
        parent: cc.find("bg/content", t.node)
      });
    });
  };
  _ctor.prototype.onToggle = function (e) {
    if (e.isChecked) {
      $2Manager.Manager.vo.userVo.dailyData.ignoreSelectAlert[this.args.viewType] = 1;
    } else {
      delete $2Manager.Manager.vo.userVo.dailyData.ignoreSelectAlert[this.args.viewType];
    }
    $2Manager.Manager.vo.saveUserData();
  };
  _ctor.prototype.onConfirm = function () {
    this.cbClickConfirm && this.cbClickConfirm();
    this.cbConfirm && this.cbConfirm();
    this.close();
  };
  _ctor.prototype.onCancel = function () {
    this.cbCancel && this.cbCancel();
    this.close();
  };
  _ctor.prototype.toCancel = function () {
    this.close();
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.RichText)], _ctor.prototype, "reasonDesc", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "wayDesc", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "confirmText", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "cancelText", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "videoIcon", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "icon", undefined);
  return cc__decorate([ccp_ccclass, $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Guide), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_SelectAlert;