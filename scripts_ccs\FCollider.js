var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StateType = exports.ColliderType = undefined;
var a;
var $2BaseEntity = require("BaseEntity");
var $2FColliderManager = require("FColliderManager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
(function (e) {
  e[e.Circle = 0] = "Circle";
  e[e.Box = 1] = "Box";
  e[e.Polygon = 2] = "Polygon";
})(a = exports.ColliderType || (exports.ColliderType = {}));
(function (e) {
  e[e.IsTest = 1] = "IsTest";
  e[e.NoTest = 2] = "NoTest";
})(exports.StateType || (exports.StateType = {}));
var def_FCollider = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.listenCom = null;
    t.isConvex = true;
    t.aabb = cc.rect();
    t.cindex = [];
    t._comp = null;
    t.isDirty = true;
    t.isActive = true;
    t.colliderId = 0;
    t.contactMap = new Map();
    t._offset = cc.v2();
    return t;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  Object.defineProperty(_ctor.prototype, "type", {
    get: function () {
      return a.Box;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "position", {
    get: function () {
      return this.node.position;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "comp", {
    get: function () {
      return this._comp;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setSize = function () {};
  _ctor.prototype.setComp = function (e) {
    this._comp = e;
  };
  _ctor.prototype.setActive = function (e, t) {
    undefined === e && (e = true);
    undefined === t && (t = 0);
    if (this.isActive == e) {
      return this;
    } else {
      return t ? this.scheduleOnce(this.setActive, t) : this.isActive = e, this;
    }
  };
  Object.defineProperty(_ctor.prototype, "x", {
    get: function () {
      return this.aabb.x;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "y", {
    get: function () {
      return this.aabb.y;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "width", {
    get: function () {
      return this.aabb.width;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "height", {
    get: function () {
      return this.aabb.height;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initCollider = function () {
    this.colliderId = o._baseId++;
    o._baseId > 5e10 && (o._baseId = 1);
  };
  Object.defineProperty(_ctor.prototype, "offset", {
    get: function () {
      return this._offset;
    },
    set: function (e) {
      this._offset = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onEnable = function () {
    var e;
    this.addCollider();
    if (this.listenCom) {
      var t = this.listenCom.getComponent($2BaseEntity.default);
      this.setComp(t);
      t.collider = this;
    } else {
      this.setComp(this.getComponent($2BaseEntity.default) || (null === (e = this.listenCom) || undefined === e ? undefined : e.getComponent($2BaseEntity.default)));
    }
  };
  _ctor.prototype.onDisable = function () {
    this.removeCollider();
    this.unscheduleAllCallbacks();
  };
  _ctor.prototype.addCollider = function () {
    $2FColliderManager.default.instance.addCollider(this);
  };
  _ctor.prototype.removeCollider = function () {
    this.isActive = false;
    $2FColliderManager.default.instance.removeCollider(this);
  };
  _ctor._baseId = 1;
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "listenCom", undefined);
  cc__decorate([ccp_property(cc.Vec2)], _ctor.prototype, "_offset", undefined);
  cc__decorate([ccp_property(cc.Vec2)], _ctor.prototype, "offset", null);
  return o = cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_FCollider;