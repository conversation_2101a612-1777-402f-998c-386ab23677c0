var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_Bullet_LigaturePonit = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.endTarget = cc.v2();
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setTarget = function (e, t) {
    this.node.height = 0;
    this.node.opacity = 0;
    this.startTarget = e;
    this.endTarget = t;
    this.resetLigature(0);
    cc.tween(this.node).to(.1, {
      opacity: 255
    }).start();
  };
  _ctor.prototype.resetLigature = function (e) {
    var t = cc.Vec2.distance(this.startTarget, this.endTarget) / this.vo.skillCfg.scale;
    var o = cc.misc.lerp(this.node.height, t, 2 * e);
    var i = $2GameUtil.GameUtil.GetAngle(this.startTarget, this.endTarget) + 90;
    this.node.setAttribute({
      height: o,
      angle: i,
      position: this.startTarget
    });
    this.collider.size.height = this.node.height;
    this.collider.offset.y = this.node.height / 2;
  };
  _ctor.prototype.onUpdate = function (t) {
    this.resetLigature(t);
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.unuse = function () {
    this.node.height = 0;
    this.node.opacity = 0;
    e.prototype.unuse.call(this);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_LigaturePonit")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_LigaturePonit;