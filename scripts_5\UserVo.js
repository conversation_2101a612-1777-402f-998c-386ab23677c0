Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UserVo = exports.DailyData = undefined;
var $2Manager = require("Manager");
var exp_DailyData = function (e) {
  this.fightcount = 0;
  this.isSign = 0;
  this.shop_refreshCount = 0;
  this.shop_refreshCd = 0;
  this.ksReward = true;
  this.freeDiamond = false;
  this.freeCoin = false;
  this.Shop_Subscribe_DayMainShow = false;
  this.isnewday = true;
  this.ttNavReward = false;
  this.TT_ShareGiftView = 2;
  this.TT_PassShare = 1;
  this.blNavReward = false;
  this.blDeskReward = false;
  this.ignoreSelectAlert = {};
  this.shopRefreshList = [0, 0];
  this.curday = e || new Date(Date.now()).getDate();
};
exports.DailyData = exp_DailyData;
var exp_UserVo = function () {
  function _ctor() {
    this.day = 0;
    this.loginDay = 0;
    this.saveDataTime = 0;
    this.isAcceptPrivacy = false;
    this.openId = "";
    this.isNewUser = true;
    this.loginCount = 0;
    this.dailyData = new exp_DailyData();
    this.gameCount = 0;
    this.inbornLv = {};
    this.unlockSpeedList = [];
    this.curusespeed = 1;
    this.guideIndex = 1;
    this.towerrewardId = 1;
    this.orderList = [];
    this.ispoplikegame = true;
    this.ispopksBar = true;
    this.ksCommonReward = false;
    this.shop_refreshTimestamp = 0;
    this.energy_refreshCd = 0;
    this.ca_code = "";
    this.unLockMode = [];
    this.knifePassLv = 0;
  }
  _ctor.prototype.updatetUserVo = function (e) {
    Object.getOwnPropertyNames(this).forEach(function (t) {
      e.hasOwnProperty(t) && (this[t] = e[t]);
    }.bind(this));
    this.checkDailyData();
    $2Manager.Manager.vo.userVo.loginCount++;
  };
  _ctor.prototype.checkDailyData = function () {
    var e = new Date(Date.now()).getDate();
    var t = new exp_DailyData(e);
    if (this.dailyData && this.dailyData.curday === e) {
      this.dailyData.isnewday = false;
      for (var o in t) {
        null == this.dailyData[o] && (this.dailyData[o] = t[o]);
      }
    } else {
      this.dailyData = t;
    }
  };
  _ctor.prototype.serializeAll = function () {
    var e = {
      loginDay: this.loginDay,
      openId: this.openId,
      day: this.day,
      saveDataTime: this.saveDataTime,
      gameCount: this.gameCount,
      unlockSpeedList: this.unlockSpeedList,
      towerrewardId: this.towerrewardId,
      dailyData: this.dailyData,
      curusespeed: this.curusespeed,
      ca_code: this.ca_code,
      unLockMode: this.unLockMode,
      knifePassLv: this.knifePassLv,
      ispoplikegame: this.ispoplikegame,
      guideIndex: this.guideIndex,
      ksCommonReward: this.ksCommonReward,
      ispopksBar: this.ispopksBar,
      shop_refreshTimestamp: this.shop_refreshTimestamp,
      energy_refreshCd: this.energy_refreshCd,
      loginCount: this.loginCount
    };
    return JSON.stringify(e);
  };
  _ctor.prototype.checkIgnorePop = function (e) {
    return this.dailyData.ignoreSelectAlert[e];
  };
  return _ctor;
}();
exports.UserVo = exp_UserVo;