var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
    value: true
});
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2UIManager = require("UIManager");
var $2EventController = require("EventController");
var $2Game = require("Game");
var $2SdkConfig = require("SdkConfig");
var $2WonderSdk = require("WonderSdk");
var $2GameUtil = require("GameUtil");
var $2ModuleLauncher = require("ModuleLauncher");
var $2SdkLauncher = require("SdkLauncher");
var $2UILauncher = require("UILauncher");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_Launcher = function(e) {
    function _ctor() {
        var t;
        var o = null !== e && e.apply(this, arguments) || this;
        o.testMode = false;
        o.CustomPlatform = $2SdkConfig.EPlatform.WEB_DEV;
        o.bmsVersion = "";
        o.progress = null;
        o.progressText = null;
        o.wonderlogo = null;
        o.gamelogo = null;
        o.logos = [];
        o.scenebg = null;
        o.softRightText = null;
        o.softICPText = null;
        o.logomap = ((t = {}).tc = 0, t.en = 1, t.th = 2, t.vn = 3, t.cn = 4, t);
        o._saveOffset = 0;
        return o;
    }
    cc__extends(_ctor, e);
    _ctor.prototype.onLoad = function() {
        var e = this;
        cc._gameManager = $2Manager.Manager;
        cc.game.addPersistRootNode(this.node);
        cc.macro.ENABLE_MULTI_TOUCH = false;
        if (cc.sys.isBrowser) {
            cc.view.enableAutoFullScreen(false);
            this.scheduleOnce(function() {
                e.fit();
            });
        }
        cc.sys.hasFont = false;
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "View", {
            Type: "show",
            Scene: "loading"
        });
        $2Manager.Manager.setPhysics(true);
        $2Manager.Manager.setPhysics(false);
    };
    _ctor.prototype.fit = function() {
        var e = cc.view.getVisibleSize();
        var t = e.width / e.height;
        var o = Math.round(100 * t);
        if (o > 57) {
            if (o >= 100) {
                cc.Canvas.instance.fitHeight = true;
                cc.Canvas.instance.fitWidth = true;
            } else {
                cc.Canvas.instance.fitHeight = true;
                cc.Canvas.instance.fitWidth = false;
            }
        }
        cc.debug.setDisplayStats(false);
    };
    _ctor.prototype.onEnable = function() {
        this.changeListener(true);
    };
    _ctor.prototype.onDisable = function() {
        this.changeListener(false);
    };
    _ctor.prototype.changeListener = function(e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.onLogin_Finish, this, $2Notifier.PriorLowest);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this, -200);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this, 200);
    };
    _ctor.prototype.lateUpdate = function() {};
    _ctor.prototype.onLogin_Finish = function() {
        this.wonderlogo.parent.destroy();
        this.gameStart();
    };
    _ctor.prototype.onOpenGame = function() {
        this.scenebg.setActive(false);
    };
    _ctor.prototype.backToMain = function() {
        this.scenebg.setActive(true);
    };
    _ctor.prototype.responsive = function() {
        var e = cc.view.getDesignResolutionSize();
        var t = cc.view.getFrameSize();
        var o = function() {
            cc.Canvas.instance.fitHeight = true;
            cc.Canvas.instance.fitWidth = true;
        };
        var i = e.width / e.height;
        var n = t.width / t.height;
        if (i < 1) {
            if (n < 1) {
                if (n > i) {
                    o();
                } else {
                    cc.Canvas.instance.fitHeight = false;
                    cc.Canvas.instance.fitWidth = true;
                }
            } else {
                o();
            }
        } else if (n > 1) {
            if (n < i) {
                o();
            } else {
                cc.Canvas.instance.fitHeight = true;
                cc.Canvas.instance.fitWidth = false;
            }
        } else {
            o();
        }
    };
    _ctor.prototype.start = function() {
        var e;
        return cc__awaiter(this, undefined, undefined, function() {
            return cc__generator(this, function(t) {
                switch (t.label) {
                    case 0:
                        this.initWonderFrameWork();
                        wonderSdk.isNative || new $2EventController.EventController();
                        this.checkPlatformInfo();
                        return [4, this.loadConfig()];
                    case 1:
                        t.sent();
                        window.tpdg = $2Cfg.Cfg.language.getAll();
                        window.initlang("tpdg");
                        this.initLanguageInfo();
                        this.gamelogo.spriteFrame = this.logos[null !== (e = this.logomap[cc.sys.language]) && undefined !== e ? e : 4];
                        wonderSdk.isIOS && this.wonderlogo.setActive(false);
                        new $2UILauncher.UILauncher();
                        this.progressText.string = cc.js.formatStr("%d%", 0);
                        this.progress.progress = 0;
                        new $2ModuleLauncher.ModuleLauncher();
                        new $2SdkLauncher.SdkLauncher(this.progressText, this.progress);
                        return [2];
                }
            });
        });
    };
    _ctor.prototype.checkPlatformInfo = function() {
        wonderSdk.isNative && (this.wonderlogo.active = false);
        this.softRightText.string = $2SdkConfig.SoftRightHodler[this.CustomPlatform];
        this.softICPText.string = $2SdkConfig.SoftICP[this.CustomPlatform];
    };
    _ctor.prototype.update = function(e) {
        $2Time.Time.update(e);
        $2UIManager.UIManager.update(e);
    };
    _ctor.prototype.initWonderFrameWork = function() {
        $2WonderSdk.WonderSdk.init(this.CustomPlatform, this.testMode);
        if (this.bmsVersion.length > 0) {
            $2SdkConfig.BMSInfoList[this.CustomPlatform].BMS_VERSION = this.bmsVersion;
            console.log("已修改Bms版本号");
        }
    };
    _ctor.prototype.loadConfig = function() {
        var e = [];
        for (var t in $2Cfg.Cfg.keyJson) {
            e.push($2Cfg.Cfg.initLocalJson(t, this.progressText, this.progress));
        }
        return Promise.all(e);
    };
    _ctor.prototype.initLanguageInfo = function() {
        var e = this.initLangCode();
        $2GameUtil.CCTool.Language.init(e, function() {
            console.log("[languageFun][init]语言包初始化完成", e);
        });
    };
    _ctor.prototype.initLangCode = function() {
        var e = cc.sys.LANGUAGE_ENGLISH;
        try {
            var t = cc.sys.language;
            var o = cc.sys.languageCode;
            cc.log("[lType]", t, o);
            if ("zh" === t) {
                if (-1 != o.indexOf("hant") || -1 != o.indexOf("tw") || -1 != o.indexOf("hk") || -1 != o.indexOf("mo")) {
                    e = "tc";
                    cc.sys.hasFont = false;
                } else {
                    e = "zh";
                }
            } else if ("ja" == t) {
                e = "jp";
                cc.sys.hasFont = false;
            } else if ("ko" == t) {
                e = "kr";
                cc.sys.hasFont = false;
            } else if (-1 != o.indexOf("vi") || -1 != o.indexOf("vn")) {
                e = "vn";
                cc.sys.hasFont = false;
            } else {
                e = -1 != o.indexOf("th") || -1 != t.indexOf("th") ? "en" : -1 != o.indexOf("id") || -1 != o.indexOf("in") ? "ina" : "en";
            }
            console.log("[Language] --> 初始化语言:  lan: " + e + " systype: " + t + " syscode: " + cc.sys.languageCode);
            return e;
        } catch (i) {}
    };
    _ctor.prototype.gameStart = function() {
        var e;
        console.log("[gameStart] 1.00", this.progress.progress);
        $2Cfg.Cfg.EquipMergeLv.forEach(function(e) {
            1 == e.lv && $2Manager.Manager.loader.preloadRes(e.res);
        });
        $2Manager.Manager.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
        Object.defineProperty(cc.game, "collisionMatrix", JSON.parse($2Manager.Manager.oldGroupMatrix));
        wonderSdk.isLive && !$2Manager.Manager.vo.userVo.ca_code && $2UIManager.UIManager.Open("ui/setting/H5CodeView");
        var t = null === (e = $2Notifier.Notifier.call($2CallID.CallID.Platform_Query)) || undefined === e ? undefined : e.mode;
        if (t) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Is_Back_From_Try_Play);
            var o = $2Cfg.Cfg.MiniGameLv.get(t);
            var i = $2Game.Game.getMouth(o.type);
            $2Notifier.Notifier.send(i.mouth, o.type, $2MVC.MVC.openArgs().setParam({
                id: o.id,
                isTryPaly: true
            }));
        } else {
            $2Notifier.Notifier.send($2ListenID.ListenID.BottomBar_OpenView, 1);
        }
        if (wonderSdk.isByteDance) {
            var n = $2Notifier.Notifier.call($2CallID.CallID.Platform_CdKey);
            $2Notifier.Notifier.send($2ListenID.ListenID.ByteDance_Check_Gift, n, true);
        } else {
            wonderSdk.isBLMicro && $2Notifier.Notifier.send($2ListenID.ListenID.Platform_CheckScene, $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene));
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "View", {
            Type: "hide",
            Scene: "loading"
        });
        cc.tween(this.scenebg).to(1, {
            opacity: 0
        }).destroySelf().start();
    };
    _ctor.isBreak = false;
    cc__decorate([ccp_property({
        displayName: "测试模式"
    })], _ctor.prototype, "testMode", undefined);
    cc__decorate([ccp_property({
        type: $2SdkConfig.EPlatform,
        displayName: "自定义平台"
    })], _ctor.prototype, "CustomPlatform", undefined);
    cc__decorate([ccp_property({
        displayName: "BMS版本号"
    })], _ctor.prototype, "bmsVersion", undefined);
    cc__decorate([ccp_property(cc.ProgressBar)], _ctor.prototype, "progress", undefined);
    cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "progressText", undefined);
    cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "wonderlogo", undefined);
    cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "gamelogo", undefined);
    cc__decorate([ccp_property([cc.SpriteFrame])], _ctor.prototype, "logos", undefined);
    cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "scenebg", undefined);
    cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "softRightText", undefined);
    cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "softICPText", undefined);
    return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_Launcher;