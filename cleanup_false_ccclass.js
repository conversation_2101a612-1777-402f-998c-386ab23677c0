const fs = require('fs');
const path = require('path');

// 读取严格验证结果
function loadStrictVerification() {
    try {
        const data = fs.readFileSync('strict_ccclass_verification.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('无法读取严格验证结果文件:', error.message);
        return null;
    }
}

// 创建备份文件夹
function createBackupDirectory() {
    const backupDir = './scripts_ccs_false_backup';
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        console.log(`创建备份文件夹: ${backupDir}`);
    }
    return backupDir;
}

// 移动文件到备份文件夹
function moveToBackup(fileName, backupDir) {
    const sourcePath = path.join('./scripts_ccs', fileName);
    const backupPath = path.join(backupDir, fileName);
    
    try {
        fs.renameSync(sourcePath, backupPath);
        return true;
    } catch (error) {
        console.error(`移动文件失败: ${fileName}`, error.message);
        return false;
    }
}

// 主函数
function main() {
    const verificationResult = loadStrictVerification();
    if (!verificationResult) {
        return;
    }
    
    const backupDir = createBackupDirectory();
    const falseCcclassFiles = verificationResult.falseCcclassFiles;
    
    console.log(`\n开始清理 ${falseCcclassFiles.length} 个非 ccclass 文件...\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    falseCcclassFiles.forEach((fileInfo, index) => {
        const fileName = fileInfo.fileName;
        
        console.log(`[${index + 1}/${falseCcclassFiles.length}] 移动到备份: ${fileName} (${fileInfo.className})`);
        console.log(`  支持特征: ${fileInfo.supportingMatches.length}, 生命周期: ${fileInfo.lifecycleMatches.length}, 总分: ${fileInfo.totalScore}`);
        
        if (moveToBackup(fileName, backupDir)) {
            successCount++;
            console.log(`  ✓ 成功移动到备份文件夹`);
        } else {
            failCount++;
            console.log(`  ✗ 移动失败`);
        }
        console.log('');
    });
    
    console.log(`=== 清理完成 ===`);
    console.log(`成功移动到备份: ${successCount} 个文件`);
    console.log(`失败: ${failCount} 个文件`);
    
    // 显示剩余的真正 ccclass 文件
    const scriptsDir = './scripts_ccs';
    if (fs.existsSync(scriptsDir)) {
        const remainingFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.js'));
        console.log(`\nscripts_ccs 文件夹中剩余 ${remainingFiles.length} 个真正的 ccclass 文件`);
        
        // 显示一些高质量的 ccclass 文件示例
        const trueCcclassFiles = verificationResult.trueCcclassFiles;
        const highQualityFiles = trueCcclassFiles
            .filter(f => f.totalScore >= 10)
            .sort((a, b) => b.totalScore - a.totalScore)
            .slice(0, 10);
        
        if (highQualityFiles.length > 0) {
            console.log(`\n=== 高质量 ccclass 文件示例 ===`);
            highQualityFiles.forEach(file => {
                console.log(`${file.fileName} - ${file.className} (分数: ${file.totalScore})`);
            });
        }
    }
    
    // 生成清理报告
    const report = {
        timestamp: new Date().toISOString(),
        cleanedFiles: falseCcclassFiles,
        backupDirectory: backupDir,
        successCount: successCount,
        failCount: failCount,
        remainingTrueCcclassFiles: verificationResult.trueCcclassFiles.length,
        totalCleaned: falseCcclassFiles.length
    };
    
    fs.writeFileSync('false_ccclass_cleanup_report.json', JSON.stringify(report, null, 2));
    console.log(`\n清理报告已保存到: false_ccclass_cleanup_report.json`);
    
    // 显示清理的文件类别
    console.log(`\n=== 清理的文件类别分析 ===`);
    const categories = {
        'State类': falseCcclassFiles.filter(f => f.fileName.includes('State')),
        'Model类': falseCcclassFiles.filter(f => f.fileName.includes('Model') || f.fileName.includes('Vo')),
        'Manager类': falseCcclassFiles.filter(f => f.fileName.includes('Manager') || f.fileName.includes('Manger')),
        '游戏模式类': falseCcclassFiles.filter(f => f.fileName.startsWith('M') && !f.fileName.startsWith('M2') && !f.fileName.startsWith('M3')),
        '其他': falseCcclassFiles.filter(f => 
            !f.fileName.includes('State') && 
            !f.fileName.includes('Model') && 
            !f.fileName.includes('Vo') &&
            !f.fileName.includes('Manager') && 
            !f.fileName.includes('Manger') &&
            !(f.fileName.startsWith('M') && !f.fileName.startsWith('M2') && !f.fileName.startsWith('M3'))
        )
    };
    
    Object.entries(categories).forEach(([category, files]) => {
        if (files.length > 0) {
            console.log(`${category}: ${files.length} 个文件`);
            files.forEach(f => console.log(`  - ${f.fileName}`));
        }
    });
}

main();
