var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2FBoxCollider = require("FBoxCollider");
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_Bullet_Ligature = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.isGradualHeight = true;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this.node.opacity = 0;
    this.node.y = -9999999;
    this.node.height = 0;
  };
  _ctor.prototype.setTarget = function (e, t) {
    this.startTarget = e;
    this.endTarget = t;
    this.resetLigature();
    cc.tween(this.node).to(.1, {
      opacity: 255
    }).start();
  };
  _ctor.prototype.resetLigature = function () {
    var e = cc.Vec2.distance(this.startTarget.position, this.endTarget.position);
    var t = this.node.height >= e ? e : this.node.height + 30;
    var o = $2GameUtil.GameUtil.GetAngle(this.startTarget.position, this.endTarget.position) + 90;
    this.node.setAttribute({
      height: this.isGradualHeight ? t : e,
      angle: o,
      position: this.startTarget.position
    });
    if (this.collider instanceof $2FBoxCollider.default) {
      this.collider.size.height = this.node.height;
      this.collider.offset.y = this.node.height / 2;
    }
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (this.isActive && this.endTarget) {
      if (this.startTarget.isActive && this.endTarget.isActive) {
        this.resetLigature();
      } else {
        this.node.y = -99999;
        this.node.opacity = 0;
        this.startTarget = null;
        this.endTarget = null;
        this._vo.lifeTime = 0;
      }
    }
  };
  _ctor.prototype.unuse = function () {
    this.endTarget = null;
    e.prototype.unuse.call(this);
  };
  cc__decorate([ccp_property({
    displayName: "是否渐进长度"
  })], _ctor.prototype, "isGradualHeight", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_Ligature")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_Ligature;