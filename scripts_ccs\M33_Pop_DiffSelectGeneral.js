var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeChainsModel = require("ModeChainsModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M33_Pop_DiffSelectGeneral = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    this.lvCfg = this.param;
    var e = this.node.getChildByPath("bg/content/Layout");
    e.children[1].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 4);
    e.children[2].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 6);
  };
  _ctor.prototype.onBtn = function (e, t) {
    var o = +t;
    $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Fight").selectDiff(o);
    this.close();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/M33_Pop_DiffSelectGeneral"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M33_Pop_DiffSelectGeneral;