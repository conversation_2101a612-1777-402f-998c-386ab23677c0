var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2NodePool = require("NodePool");
var $2Notifier = require("Notifier");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_LifeLabel = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.posType = 1;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "string", {
    get: function () {
      return this.label.string;
    },
    set: function (e) {
      this.isValid && (this.label.string = e);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.set = function (e) {
    this.offset = null;
    this.changeListener(false);
    this.label = this.node.getComponent(cc.Label);
    this.ower = e;
    this.changeListener(true);
  };
  _ctor.prototype.changeListener = function (e) {
    var t;
    null === (t = this.ower) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUpdate, this.onOwerUpdate, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
  };
  _ctor.prototype.onFight_RoundState = function () {
    this.remove();
  };
  _ctor.prototype.onOwerUpdate = function () {
    if (this.ower.curHp <= 0 || !this.ower.isActive) {
      return this.remove();
    }
    var e = 1 == this.posType ? this.ower.haedPosition : this.ower.bodyPosition;
    this.offset && e.addSelf(this.offset);
    this.node.setPosition(e);
  };
  _ctor.prototype.remove = function () {
    this.changeListener(false);
    this.isValid && $2NodePool.NodePool.despawn(this.node.nodeItem);
    this.ower = null;
  };
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_LifeLabel;