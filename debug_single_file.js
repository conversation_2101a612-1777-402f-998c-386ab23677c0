const fs = require('fs');
const path = require('path');

// 调试单个文件的分析
function debugSingleFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        console.log(`=== 调试文件: ${filePath} ===\n`);
        
        // 检查各种模式
        const patterns = {
            'cc._decorator': /cc\._decorator/g,
            'ccp_ccclass': /ccp_ccclass/g,
            'cc__decorator.ccclass': /cc__decorator\.ccclass/g,
            'ccp_property': /ccp_property/g,
            'cc__decorator.property': /cc__decorator\.property/g,
            'ccp_menu': /ccp_menu/g,
            'cc.Component': /cc\.Component/g,
            'exp_class': /var\s+exp_(\w+)\s*=\s*function\s*\(/g,
            'exports_undefined': /exports\.(\w+)\s*=\s*undefined/g,
            '__decorate': /__decorate/g,
            'cc__extends': /cc__extends/g
        };
        
        console.log('=== 模式匹配结果 ===');
        for (const [name, pattern] of Object.entries(patterns)) {
            const matches = content.match(pattern);
            console.log(`${name}: ${matches ? matches.length : 0} 个匹配`);
            if (matches && matches.length > 0) {
                console.log(`  匹配内容: ${matches.slice(0, 3).join(', ')}${matches.length > 3 ? '...' : ''}`);
            }
        }
        
        // 检查我的原始分析逻辑
        const expClassMatches = content.match(/var\s+exp_(\w+)\s*=\s*function\s*\(/g) || [];
        const exportsMatches = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];
        
        console.log('\n=== 原始分析逻辑 ===');
        console.log(`exp_class 匹配: ${expClassMatches.length}`);
        console.log(`exports 匹配: ${exportsMatches.length}`);
        console.log(`总定义数: ${expClassMatches.length + exportsMatches.length}`);
        console.log(`是否为单类: ${(expClassMatches.length + exportsMatches.length) === 1}`);
        
        // 检查 ccclass 特征分数
        const ccclassIndicators = [
            /@ccclass/,
            /__decorate\s*\(\s*\[\s*ccclass/,
            /cc\.Class\s*\(/,
            /cc\.Component/,
            /cc\.Node/,
            /@property/,
            /cc\._decorator/
        ];
        
        const lifecycleMethods = [
            /onLoad\s*[:=]\s*function/,
            /start\s*[:=]\s*function/,
            /update\s*[:=]\s*function/,
            /lateUpdate\s*[:=]\s*function/,
            /onEnable\s*[:=]\s*function/,
            /onDisable\s*[:=]\s*function/,
            /onDestroy\s*[:=]\s*function/
        ];
        
        let ccclassScore = 0;
        console.log('\n=== ccclass 特征检查 ===');
        ccclassIndicators.forEach((pattern, index) => {
            if (pattern.test(content)) {
                ccclassScore += 2;
                console.log(`✓ ccclass 指标 ${index + 1}: 匹配 (+2 分)`);
            } else {
                console.log(`✗ ccclass 指标 ${index + 1}: 不匹配`);
            }
        });
        
        lifecycleMethods.forEach((pattern, index) => {
            if (pattern.test(content)) {
                ccclassScore += 1;
                console.log(`✓ 生命周期方法 ${index + 1}: 匹配 (+1 分)`);
            }
        });
        
        console.log(`\nccclass 总分数: ${ccclassScore}`);
        
        // 检查非 ccclass 特征
        const nonCcclassIndicators = [
            /exports\.\w+\s*=\s*function\s*\(/,
            /exports\.\w+\s*=\s*["'\d]/,
            /window\.(wx|tt|qq|qg|ks)/,
            /\$2Request\.default\.post/
        ];
        
        let nonCcclassScore = 0;
        console.log('\n=== 非 ccclass 特征检查 ===');
        nonCcclassIndicators.forEach((pattern, index) => {
            if (pattern.test(content)) {
                nonCcclassScore += 1;
                console.log(`✓ 非 ccclass 指标 ${index + 1}: 匹配 (+1 分)`);
            } else {
                console.log(`✗ 非 ccclass 指标 ${index + 1}: 不匹配`);
            }
        });
        
        console.log(`\n非 ccclass 总分数: ${nonCcclassScore}`);
        
        console.log('\n=== 最终判断 ===');
        console.log(`是否可能是 ccclass: ${ccclassScore > 0 && ccclassScore > nonCcclassScore}`);
        console.log(`是否确定不是 ccclass: ${nonCcclassScore > ccclassScore && nonCcclassScore > 0}`);
        
    } catch (error) {
        console.error('分析文件时出错:', error.message);
    }
}

// 调试指定文件
debugSingleFile('./scripts_5/M20_Pop_EquipInfo.js');
