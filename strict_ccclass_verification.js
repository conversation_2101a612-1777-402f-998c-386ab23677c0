const fs = require('fs');
const path = require('path');

// 严格验证 ccclass 文件
function strictCcclassVerification(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 必须包含的 ccclass 关键特征 (至少一个)
        const requiredCcclassFeatures = [
            /cc__decorator\.ccclass/,           // cc__decorator.ccclass
            /ccp_ccclass\s*=\s*cc__decorator\.ccclass/, // ccp_ccclass = cc__decorator.ccclass
            /@ccclass/,                        // @ccclass 装饰器
            /cc__decorate\s*\(\s*\[\s*ccp_ccclass/, // cc__decorate([ccp_ccclass
            /cc__decorate\s*\(\s*\[\s*ccclass/     // cc__decorate([ccclass
        ];
        
        // 支持特征 (增加可信度)
        const supportingFeatures = [
            /cc\._decorator/,                  // cc._decorator
            /ccp_property/,                    // ccp_property
            /ccp_menu/,                        // ccp_menu
            /cc__decorator\.property/,         // cc__decorator.property
            /cc__decorator\.menu/,             // cc__decorator.menu
            /cc\.Component/,                   // 继承自 cc.Component
            /cc\.Node/                         // 继承自 cc.Node
        ];
        
        // 生命周期方法 (Cocos Creator 组件特有)
        const lifecycleMethods = [
            /onLoad\s*[:=]\s*function/,
            /start\s*[:=]\s*function/,
            /update\s*[:=]\s*function/,
            /lateUpdate\s*[:=]\s*function/,
            /onEnable\s*[:=]\s*function/,
            /onDisable\s*[:=]\s*function/,
            /onDestroy\s*[:=]\s*function/
        ];
        
        // 检查必需特征
        let hasRequiredFeature = false;
        let requiredMatches = [];
        
        requiredCcclassFeatures.forEach((pattern, index) => {
            if (pattern.test(content)) {
                hasRequiredFeature = true;
                requiredMatches.push(`必需特征${index + 1}`);
            }
        });
        
        // 检查支持特征
        let supportingScore = 0;
        let supportingMatches = [];
        
        supportingFeatures.forEach((pattern, index) => {
            if (pattern.test(content)) {
                supportingScore += 2;
                supportingMatches.push(`支持特征${index + 1}`);
            }
        });
        
        // 检查生命周期方法
        let lifecycleScore = 0;
        let lifecycleMatches = [];
        
        lifecycleMethods.forEach((pattern, index) => {
            if (pattern.test(content)) {
                lifecycleScore += 1;
                lifecycleMatches.push(`生命周期${index + 1}`);
            }
        });
        
        // 检查类定义结构
        const classDefinitions = content.match(/var\s+(def_|exp_)(\w+)\s*=\s*function\s*\(/g) || [];
        const exportsDefinitions = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];
        
        const className = classDefinitions.length > 0 ? 
            classDefinitions[0].match(/var\s+(?:def_|exp_)(\w+)/)[1] : 
            (exportsDefinitions.length > 0 ? 
                exportsDefinitions[0].match(/exports\.(\w+)/)[1] : '未知');
        
        // 严格判断: 必须有必需特征才算 ccclass
        const isStrictCcclass = hasRequiredFeature;
        const totalScore = supportingScore + lifecycleScore;
        
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            className: className,
            isStrictCcclass: isStrictCcclass,
            hasRequiredFeature: hasRequiredFeature,
            requiredMatches: requiredMatches,
            supportingMatches: supportingMatches,
            lifecycleMatches: lifecycleMatches,
            totalScore: totalScore,
            classDefinitions: classDefinitions.length,
            exportsDefinitions: exportsDefinitions.length
        };
    } catch (error) {
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            error: error.message,
            isStrictCcclass: false
        };
    }
}

// 验证 scripts_ccs 文件夹中的所有文件
function verifyScriptsCcs() {
    const scriptsDir = './scripts_ccs';
    
    if (!fs.existsSync(scriptsDir)) {
        console.log('scripts_ccs 文件夹不存在');
        return;
    }
    
    const files = fs.readdirSync(scriptsDir);
    const jsFiles = files.filter(file => file.endsWith('.js')).map(file => path.join(scriptsDir, file));
    
    console.log(`严格验证 scripts_ccs 文件夹中的 ${jsFiles.length} 个文件...\n`);
    
    const trueCcclassFiles = [];
    const falseCcclassFiles = [];
    const errorFiles = [];
    
    jsFiles.forEach(filePath => {
        const verification = strictCcclassVerification(filePath);
        
        if (verification.error) {
            errorFiles.push(verification);
            console.log(`❌ 错误: ${verification.fileName} - ${verification.error}`);
            return;
        }
        
        if (verification.isStrictCcclass) {
            trueCcclassFiles.push(verification);
            console.log(`✅ 真ccclass: ${verification.fileName} - ${verification.className} (${verification.requiredMatches.join(', ')})`);
        } else {
            falseCcclassFiles.push(verification);
            console.log(`❌ 非ccclass: ${verification.fileName} - ${verification.className} (分数: ${verification.totalScore})`);
        }
    });
    
    console.log(`\n=== 严格验证结果 ===`);
    console.log(`真正的 ccclass 文件: ${trueCcclassFiles.length} 个`);
    console.log(`非 ccclass 文件: ${falseCcclassFiles.length} 个`);
    console.log(`错误文件: ${errorFiles.length} 个`);
    
    if (falseCcclassFiles.length > 0) {
        console.log(`\n=== 需要移除的非 ccclass 文件 ===`);
        falseCcclassFiles.forEach(file => {
            console.log(`${file.fileName} - ${file.className} (支持特征: ${file.supportingMatches.length}, 生命周期: ${file.lifecycleMatches.length})`);
        });
    }
    
    // 保存验证结果
    const result = {
        trueCcclassFiles: trueCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.className,
            requiredMatches: f.requiredMatches,
            supportingMatches: f.supportingMatches,
            lifecycleMatches: f.lifecycleMatches,
            totalScore: f.totalScore
        })),
        falseCcclassFiles: falseCcclassFiles.map(f => ({
            fileName: f.fileName,
            className: f.className,
            supportingMatches: f.supportingMatches,
            lifecycleMatches: f.lifecycleMatches,
            totalScore: f.totalScore
        })),
        errorFiles: errorFiles.map(f => ({
            fileName: f.fileName,
            error: f.error
        }))
    };
    
    fs.writeFileSync('strict_ccclass_verification.json', JSON.stringify(result, null, 2));
    console.log(`\n严格验证结果已保存到: strict_ccclass_verification.json`);
}

verifyScriptsCcs();
