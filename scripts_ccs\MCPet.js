var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2AlertManager = require("AlertManager");
var $2DragonBody = require("DragonBody");
var $2Pet = require("Pet");
var $2MCBoss = require("MCBoss");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MCPet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._bossHurtDt = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return this.property.base.Scale;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setPet = function (t, o) {
    e.prototype.setPet.call(this, t, o);
    this.game.createLifeBar(this, {});
    $2AlertManager.AlertManager.showDialogBox("help!!", {
      parent: this.node
    });
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t = e.comp;
    if (t instanceof $2DragonBody.default && t.curIndex) {
      var o = t.owerChains.getHurt();
      this.behit(o);
    } else {
      t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
      }));
    }
  };
  _ctor.prototype.onCollisionStay = function (e) {
    var t = e.comp;
    t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
      hurCd: 1,
      hid: t.ID
    }));
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/MCPet")], _ctor);
}($2Pet.default);
exports.default = def_MCPet;