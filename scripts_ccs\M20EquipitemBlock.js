var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2M20Equipitem = require("M20Equipitem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20EquipitemBlock = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.lockinfo = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setInfo = function (t) {
    e.prototype.setInfo.call(this, t);
    var o = $2Cfg.Cfg.EquipLv.filter({
      equipId: t
    });
    var i = $2Cfg.Cfg.RoleUnlock.find({
      id: t
    });
    this.equipcfg = i;
    this.eueiplvcfgs = o;
  };
  _ctor.prototype.resetState = function () {
    e.prototype.resetState.call(this);
    var t = this.mode.fragmentsPack.getVal(this.equipcfg.id);
    if (1 == this.equipcfg.unlock) {
      this.lockinfo.string = cc.js.formatStr("通关章节%d解锁", this.equipcfg.Count);
    } else if (2 == this.equipcfg.unlock) {
      this.lockinfo.string = cc.js.formatStr("%d个碎片解锁", this.equipcfg.Count - t);
    } else {
      3 == this.equipcfg.unlock && (this.lockinfo.string = cc.js.formatStr("%d个视频解锁", this.equipcfg.Count));
    }
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "lockinfo", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}($2M20Equipitem.default);
exports.default = def_M20EquipitemBlock;