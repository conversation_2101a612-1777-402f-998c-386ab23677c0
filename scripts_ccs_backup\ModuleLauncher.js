Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ModuleLauncher = undefined;
var $2BottomBarController = require("BottomBarController");
var $2FightController = require("FightController");
var $2BuffController = require("BuffController");
var $2SkillController = require("SkillController");
var $2LoadingController = require("LoadingController");
var $2SettingController = require("SettingController");
var $2ModeBackpackHeroController = require("ModeBackpackHeroController");
var $2ItemController = require("ItemController");
var $2GuidesController = require("GuidesController");
var $2ShopController = require("ShopController");
var $2PayController = require("PayController");
var $2ADController = require("ADController");
var $2RBadgeController = require("RBadgeController");
var $2ModeThrowingKnifeController = require("ModeThrowingKnifeController");
var $2ModePickUpBulletsController = require("ModePickUpBulletsController");
var $2ModeBulletsReboundController = require("ModeBulletsReboundController");
var $2ModeChainsController = require("ModeChainsController");
var $2TideDefendController = require("TideDefendController");
var $2ModeManGuardsController = require("ModeManGuardsController");
var $2ModeAllOutAttackController = require("ModeAllOutAttackController");
var $2ModeDragonWarController = require("ModeDragonWarController");
exports.ModuleLauncher = function () {
  new $2RBadgeController.RBadgeController();
  new $2LoadingController.LoadingController();
  new $2GuidesController.GuidesController();
  new $2SettingController.SettingController();
  new $2ItemController.ItemController();
  new $2ShopController.ShopController();
  new $2PayController.PayController();
  new $2ADController.ADController();
  new $2BottomBarController.BottomBarController();
  new $2FightController.FightController();
  new $2SkillController.SkillController();
  new $2BuffController.BuffController();
  new $2ModeBackpackHeroController.ModeBackpackHeroController();
  new $2ModeThrowingKnifeController.ModeThrowingKnifeController();
  new $2ModePickUpBulletsController.ModePickUpBulletsController();
  new $2ModeBulletsReboundController.ModeBulletsReboundController();
  new $2ModeChainsController.ModeChainsController();
  new $2TideDefendController.TideDefendController();
  new $2ModeManGuardsController.ModeManGuardsController();
  new $2ModeAllOutAttackController.ModeAllOutAttackController();
  new $2ModeDragonWarController.ModeDragonWarController();
};