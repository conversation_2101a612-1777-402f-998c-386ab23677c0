{"probablyCcclassFiles": [{"fileName": "Buff.js", "className": "Buff", "ccclassScore": 1}, {"fileName": "FCollider.js", "className": "ColliderType", "ccclassScore": 8}, {"fileName": "GridView.js", "className": "GRID_TYPE", "ccclassScore": 8}, {"fileName": "MoreGamesView.js", "className": "MoreGames", "ccclassScore": 2}, {"fileName": "OrganismBase.js", "className": "nullMap", "ccclassScore": 4}, {"fileName": "PropertyVo.js", "className": "Property", "ccclassScore": 2}, {"fileName": "TrackManger.js", "className": "TrackManger", "ccclassScore": 1}], "definitelyNotCcclassFiles": [{"fileName": "Api.js", "className": "click", "nonCcclassScore": 2}, {"fileName": "config.js", "className": "API_SECRET", "nonCcclassScore": 1}, {"fileName": "index.js", "className": "minSdk", "nonCcclassScore": 1}, {"fileName": "ModeBackpackHeroModel.js", "className": "ActivityPass", "nonCcclassScore": 1}, {"fileName": "ModuleLauncher.js", "className": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "nonCcclassScore": 1}, {"fileName": "UILauncher.js", "className": "UILauncher", "nonCcclassScore": 1}], "uncertainFiles": [{"fileName": "BulletVoPool.js", "className": "BulletVoPool", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "Cfg.js", "className": "Cfg", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "KnapsackVo.js", "className": "KnapsackVo", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "LatticeMap.js", "className": "LatticeMap", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "LevelMgr.js", "className": "Level", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MBackpackHero.js", "className": "MBPack", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MBRebound.js", "className": "MBRebound", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MCBossState.js", "className": "MCBossState", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MChains.js", "className": "<PERSON><PERSON><PERSON>", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MMGuards.js", "className": "<PERSON><PERSON><PERSON><PERSON>", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MonsterState.js", "className": "MonsterState", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MonsterTidalState.js", "className": "MonsterTidalState", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MTideDefendRebound.js", "className": "MTideDefendRebound", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "MTKnife.js", "className": "MTKnife", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "PetState.js", "className": "PetState", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "RBadgeModel.js", "className": "RBadge", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "RewardEvent.js", "className": "RewardEvent", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "RoleState.js", "className": "RoleState", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "SkillManager.js", "className": "Skill", "ccclassScore": 0, "nonCcclassScore": 0}, {"fileName": "TaskModel.js", "className": "TaskSaveType", "ccclassScore": 0, "nonCcclassScore": 0}]}