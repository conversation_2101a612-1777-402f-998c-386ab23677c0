var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var a;
var $2SdkConfig = require("SdkConfig");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
(function (e) {
  e[e.SHOW = 0] = "SHOW";
  e[e.HIDE = 1] = "HIDE";
})(a || (a = {}));
cc.Enum(a);
var def_VisibleComponent = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.showType = a.HIDE;
    t.platform = [];
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    var e = false;
    -1 != this.platform.indexOf(wonderSdk.platformId) && (e = true);
    if (this.showType == a.HIDE) {
      this.node.active = !e;
    } else {
      this.node.active = e;
    }
  };
  cc__decorate([ccp_property({
    type: a
  })], _ctor.prototype, "showType", undefined);
  cc__decorate([ccp_property({
    type: [$2SdkConfig.EPlatform],
    displayName: "平台"
  })], _ctor.prototype, "platform", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/VisibleComponent")], _ctor);
}(cc.Component);
exports.default = def_VisibleComponent;