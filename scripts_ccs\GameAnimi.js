var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;
var def_GameAnimi = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.type = $2GameSeting.GameSeting.TweenType.Game;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.set = function (e) {
    this.type = e;
    return this;
  };
  _ctor.prototype.onEnable = function () {
    e.prototype.onEnable.call(this);
    this.type == $2GameSeting.GameSeting.TweenType.Game && $2Game.Game.mgr && (this.defaultClip.speed = $2Game.Game.mgr.gameSpeed);
    this.play();
  };
  cc__decorate([ccp_property({
    type: cc.Enum($2GameSeting.GameSeting.TweenType)
  })], _ctor.prototype, "type", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/GameAnimi")], _ctor);
}(cc.Animation);
exports.default = def_GameAnimi;