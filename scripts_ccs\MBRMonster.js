var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2Monster = require("Monster");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2ModeBulletsReboundModel = require("ModeBulletsReboundModel");
var $2MBRebound = require("MBRebound");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MBRMonster = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.fireAngle = 0;
    t.firePower = 0;
    t.aimIng = false;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    var t = $2Cfg.Cfg.Monster.get(this.lvCfg.monId[0]);
    t.skill && this.skillMgr.add(t.skill[0], false);
    this.knifeController = new $2MBRebound.MBRebound.KnifeController().setAttribute({
      ower: this
    });
  };
  _ctor.prototype.setAmClip = function () {
    var e = this;
    this.mySkeleton.skeletonData = null;
    $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
      e.mySkeleton.reset(t);
      e.playAction("idle", true);
      e.mySkeleton.setCompleteListener(function () {
        "attack" == e.mySkeleton.animation && e.playAction("idle", true);
      });
      e.delayByGame(function () {
        e.onNewSize(e.roleNode.getContentSize());
      });
    });
  };
  _ctor.prototype.onNewSize = function (t) {
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.registerState = function () {};
  _ctor.prototype.toIdle = function () {};
  _ctor.prototype.behit = function (t) {
    this.mySkeleton.playQueue(["hit", "idle"], true);
    return e.prototype.behit.call(this, t);
  };
  _ctor.prototype.toBeHit = function () {};
  _ctor.prototype.toDead = function () {
    this.game.showEntityDieEffect(2, {
      position: this.position,
      scale: this.monCfg.Scale
    });
    this.node.emit($2ListenID.ListenID.Fight_Dead);
    this.droppedItems();
    if (this.monCfg.skill) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, true);
      cc.tween(this.node).parallel(cc.tween().to(.1, {
        angle: -30
      }), cc.tween().bezierTo(.4, this.position, this.position.add(cc.v2(400, 300)), this.position.add(cc.v2(800, 0)))).start();
    } else {
      this.isActive = false;
      this.isDead = true;
    }
  };
  _ctor.prototype.aiFire = function () {
    var e = this;
    var t = $2Game.Game.tween(this);
    this.aimIng = true;
    var o = $2GameUtil.GameUtil.random(2, 6);
    for (var i = 0; i < o; i++) {
      t.to($2GameUtil.GameUtil.random(30, 50) / 100, {
        fireAngle: -90 - $2GameUtil.GameUtil.random(30, 70),
        firePower: $2GameUtil.GameUtil.random(50, 200)
      }, {
        easing: cc.easing.sineInOut
      });
    }
    var n = this.game.miniGameCfg.hitRate;
    var r = this.game.miniGameCfg.hitPowAng[0];
    var a = 30 * (1 - (n[this.knifeController.fireNum] || n.lastVal));
    t.to(.3, {
      fireAngle: r[1] + $2GameUtil.GameUtil.random(-a, a),
      firePower: r[0] + 2 * $2GameUtil.GameUtil.random(-a, a)
    }, {
      easing: cc.easing.sineInOut
    });
    t.delay(1).call(function () {
      e.aimIng = false;
      e.knifeController.fire(.15);
    }).start();
  };
  _ctor.prototype.update = function () {
    this.aimIng && (this.knifeController.aimData = {
      angle: this.fireAngle,
      power: this.firePower
    });
  };
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBulletsReboundModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.droppedItems = function () {
    var e = this;
    this.lvCfg.dropExpRatio && this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (t) {
      if (t.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect) {
        var o = 2 == t.num ? $2MBRebound.MBRebound.poolType.HighBuff : $2MBRebound.MBRebound.poolType.NormalBuff;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
          type: o,
          getPool: function (t) {
            return e.mode.fightBuffWidth(o, t);
          }
        }).setDailyTime(.3));
      }
    });
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBulletsRebound/MBRMonster")], _ctor);
}($2Monster.Monster);
exports.default = def_MBRMonster;