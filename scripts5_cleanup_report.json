{"timestamp": "2025-07-09T03:18:23.862Z", "originalScripts5Count": 286, "scriptsCcsCount": 114, "duplicatesFound": 114, "successfullyDeleted": 114, "failedToDelete": 0, "remainingInScripts5": 172, "deletedFiles": ["ArcBullet.js", "AutoAmTool.js", "AutoAnimationClip.js", "AutoFollow.js", "BackHeroProp.js", "BackpackHeroHome.js", "BoomerangBullet.js", "BounceBullet.js", "BuffCardItem.js", "Bullet.js", "BulletBase.js", "Bullet_Arrow.js", "Bullet_FollowTarget.js", "Bullet_HitReflex.js", "Bullet_Laser.js", "Bullet_Ligature.js", "Bullet_LigaturePonit.js", "Bullet_Path.js", "Bullet_RandomMove.js", "Bullet_RigidBody.js", "CircleBullet.js", "Commonguide.js", "ContinuousBullet.js", "DialogBox.js", "DragonBody.js", "EffectSkeleton.js", "Effect_Behead.js", "Effect_Behit.js", "EnergyStamp.js", "EntityDieEffect.js", "ExchangeCodeView.js", "FBoxCollider.js", "FCircleCollider.js", "FCollider.js", "FPolygonCollider.js", "GameAnimi.js", "GameEffect.js", "GameSkeleton.js", "GiftPackView.js", "Goods.js", "GoodsUIItem.js", "GridView.js", "GridViewCell.js", "LaserRadiationBullet.js", "Launcher.js", "LifeBar.js", "LifeLabel.js", "LigatureBullet.js", "M20Equipitem.js", "M20EquipitemBlock.js", "M20EquipitemList.js", "M20Gooditem.js", "M20Prop.js", "M20Prop_Equip.js", "M20Prop_Gemstone.js", "M20_PartItem.js", "M20_Pop_EquipInfo.js", "M20_Pop_GameRewardView.js", "M20_Pop_GetBox.js", "M20_Pop_GetEnergy.js", "M20_Pop_Insufficient_Props_Tips.js", "M20_Pop_NewEquipUnlock.js", "M20_Pop_ShopBoxInfo.js", "M20_Pop_ShopBuyConfirm.js", "M20_PrePare_Activity.js", "M20_PrePare_Equip.js", "M20_PrePare_Fight.js", "M20_PrePare_MenuView.js", "M20_PrePare_Shop.js", "M20_ShopPartItem.js", "M20_ShopPartItem_adcoupon.js", "M20_ShopPartItem_box.js", "M20_ShopPartItem_coin.js", "M20_ShopPartItem_daily.js", "M20_ShopPartItem_hero.js", "M20_Shop_HeroItem.js", "M33_FightBuffView.js", "M33_Pop_DiffSelectGeneral.js", "M33_Pop_GameEnd.js", "M33_Pop_Revive.js", "M33_TestBox.js", "MBRMonster.js", "MBRRole.js", "MCPet.js", "MCRole.js", "MMGMonster.js", "MMGRole.js", "MonsterTideDefend.js", "MoreGamesItem.js", "MoreGamesView.js", "MoveEntity.js", "MoveImg.js", "MovingBGSprite.js", "MTideDefendRmod.js", "MTKRole.js", "NPC.js", "OrganismBase.js", "Pet.js", "RBadgePoint.js", "ReflexBullet.js", "Role.js", "SelectAlert.js", "ShareButton.js", "SkeletonBullet.js", "TestItem.js", "ThrowBullet.js", "TornadoBullet.js", "TrackBullet.js", "TrackItem.js", "VideoButton.js", "VideoIcon.js", "VisibleComponent.js", "WallBase.js", "Weather.js"], "remainingFiles": ["ADController.js", "ADModel.js", "AlertManager.js", "Api.js", "AssetLoader.js", "AutoScaleComponent.js", "BagModeSkillPoolCfg.js", "bagMonsterLvCfg.js", "BagShopItemCfg.js", "BagSkillCfg.js", "BottomBarController.js", "BottomBarModel.js", "BottomBarView.js", "BoxLevelExpCfg.js", "BronMonsterManger.js", "Buff.js", "BuffCfg.js", "BuffController.js", "BuffList.js", "BuffModel.js", "BuildModeSkiilpoolCfg.js", "BulletEffectCfg.js", "BulletVoPool.js", "ByteDance.js", "Cfg.js", "CompManager.js", "config.js", "CurrencyConfigCfg.js", "dmmItemCfg.js", "dmmRoleCfg.js", "Dragon.js", "dragonPathCfg.js", "DropConfigCfg.js", "EaseScaleTransition.js", "EquipLvCfg.js", "EquipMergeLvCfg.js", "EventController.js", "EventModel.js", "FColliderManager.js", "FightController.js", "FightModel.js", "FightScene.js", "FightUIView.js", "Game.js", "GameatrCfg.js", "GameCamera.js", "GameSettingCfg.js", "GridViewFreshWork.js", "GTSimpleSpriteAssembler2D.js", "GuideCfg.js", "GuidesController.js", "GuidesModel.js", "index.js", "IOSSdk.js", "ItemController.js", "ItemModel.js", "JUHEAndroid.js", "KnapsackVo.js", "languageCfg.js", "LanguageFun.js", "LatticeMap.js", "LevelExpCfg.js", "LevelMgr.js", "LoadingController.js", "LoadingModel.js", "LoadingView.js", "LocalStorage.js", "LvInsideCfg.js", "LvOutsideCfg.js", "M33_FightScene.js", "M33_FightUIView.js", "MapCfg.js", "MBackpackHero.js", "MBRebound.js", "MCBoss.js", "MCBossState.js", "MCDragoMutilation.js", "MCDragon.js", "MChains.js", "MiniGameEquipCfg.js", "MiniGameLvCfg.js", "MMGuards.js", "ModeAllOutAttackController.js", "ModeAllOutAttackModel.js", "ModeBackpackHeroController.js", "ModeBackpackHeroModel.js", "ModeBulletsReboundController.js", "ModeBulletsReboundModel.js", "ModeChainsController.js", "ModeChainsModel.js", "ModeDragonWarController.js", "ModeDragonWarModel.js", "ModeManGuardsController.js", "ModeManGuardsModel.js", "ModePickUpBulletsController.js", "ModePickUpBulletsModel.js", "ModeThrowingKnifeController.js", "ModeThrowingKnifeModel.js", "ModuleLauncher.js", "MonstarTideDragon.js", "Monster.js", "MonsterCfg.js", "MonsterElite.js", "MonsterLvCfg.js", "MonsterState.js", "MonsterTidal.js", "MonsterTidalBoss.js", "MonsterTidalState.js", "MovingBGAssembler.js", "MTideDefendRebound.js", "MTKnife.js", "NativeAndroid.js", "NotifyCaller.js", "NotifyListener.js", "PayController.js", "PayModel.js", "PayShopCfg.js", "PetState.js", "PoolListCfg.js", "Pop.js", "ProcessRewardsCfg.js", "PropertyVo.js", "randomNameCfg.js", "RBadgeController.js", "RBadgeModel.js", "Report.js", "ReportQueue.js", "ResUtil.js", "RewardEvent.js", "RoleCfg.js", "RoleLvCfg.js", "RoleSkillList.js", "RoleState.js", "RoleUnlockCfg.js", "SdkLauncher.js", "SettingController.js", "SettingModel.js", "SettingView.js", "ShopController.js", "ShopModel.js", "signCfg.js", "SkiilpoolCfg.js", "SkillCfg.js", "SkillController.js", "SkillManager.js", "SkillModel.js", "SkillModule.js", "SoundCfg.js", "TaskCfg.js", "TaskModel.js", "TaskTypeCfg.js", "TestController.js", "TestModel.js", "TestView.js", "TideDefendController.js", "TideDefendModel.js", "TimeManage.js", "TowerAmethystRewardCfg.js", "TowerCfg.js", "TowerCoinRewardCfg.js", "TowerLvCfg.js", "TowerMenuCfg.js", "TrackManger.js", "ttPostbackCtl.js", "TwoDHorizontalLayoutObject.js", "TwoDLayoutObject.js", "UILauncher.js", "UserVo.js", "Vehicle.js", "WeatherCfg.js", "WebDev.js", "WonderSdk.js"]}