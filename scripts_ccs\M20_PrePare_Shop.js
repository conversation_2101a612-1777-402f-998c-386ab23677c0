var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20_ShopPartItem = require("M20_ShopPartItem");
var $2M20_ShopPartItem_adcoupon = require("M20_ShopPartItem_adcoupon");
var $2M20_ShopPartItem_box = require("M20_ShopPartItem_box");
var $2M20_ShopPartItem_coin = require("M20_ShopPartItem_coin");
var $2M20_ShopPartItem_daily = require("M20_ShopPartItem_daily");
var $2M20_ShopPartItem_hero = require("M20_ShopPartItem_hero");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_PrePare_Shop = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.contentNode = null;
    t.shopdata = [];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeCall(t, $2CallID.CallID.M20_GetShopView, this.getThis, this);
  };
  _ctor.prototype.onLoad = function () {
    var e;
    var t;
    this.shopdata = [{
      title: "每日商店",
      refreshCount: $2Manager.Manager.vo.switchVo.refreshSetting[1],
      refreshCd: $2Manager.Manager.vo.switchVo.refreshSetting[0],
      contentcomp: "ui/ModeBackpackHero/ShopPartItem_tick",
      comp: "M20_ShopPartItem_daily",
      prefabe: "ui/ModeBackpackHero/partitem"
    }];
    wonderSdk.hasPay && (e = this.shopdata).push.apply(e, [{
      title: "免广告券",
      prefabe: "ui/ModeBackpackHero/partitem",
      contentcomp: "ui/ModeBackpackHero/ShopPartItem",
      comp: "M20_ShopPartItem_adcoupon"
    }]);
    (t = this.shopdata).push.apply(t, [{
      title: "宝箱",
      prefabe: "ui/ModeBackpackHero/partitemhigh",
      contentcomp: "ui/ModeBackpackHero/ShopPartItem",
      comp: "M20_ShopPartItem_box"
    }, {
      title: "灵石",
      prefabe: "ui/ModeBackpackHero/partitem",
      contentcomp: "ui/ModeBackpackHero/ShopPartItem",
      comp: "M20_ShopPartItem_coin"
    }]);
  };
  _ctor.prototype.refreshDailtData = function () {};
  _ctor.prototype.loadData = function () {
    var e;
    var t = this;
    (e = {}).M20_ShopPartItem_box = $2M20_ShopPartItem_box.default;
    e.M20_ShopPartItem_adcoupon = $2M20_ShopPartItem_adcoupon.default;
    e.M20_ShopPartItem_coin = $2M20_ShopPartItem_coin.default;
    e.M20_ShopPartItem_daily = $2M20_ShopPartItem_daily.default;
    e.M20_ShopPartItem_hero = $2M20_ShopPartItem_hero.default;
    var o = e;
    var i = function (e) {
      var i = n.shopdata[e];
      $2Manager.Manager.loader.loadPrefab(i.contentcomp, n.node).then(function (n) {
        var r = n;
        var a = r.getComponent($2M20_ShopPartItem.default);
        a || (a = r.addComponent(o[i.comp]));
        r.setAttribute({
          parent: t.contentNode,
          zIndex: e
        });
        a.contentnode = r.getChildByName("list");
        a.title = r.getChildByName("title_store").getComponentInChildren(cc.Label);
        a.setData(t.shopdata[e]);
        t.contentNode.getComponent(cc.Layout).updateLayout();
      });
    };
    var n = this;
    for (var r = 0; r < this.shopdata.length; r++) {
      i(r);
    }
  };
  _ctor.prototype.getThis = function () {
    return this;
  };
  _ctor.prototype.onOpen = function () {
    this.node.opacity = 0;
    this.loadData();
  };
  _ctor.prototype.onShowFinish = function () {
    var e;
    var t;
    null === (t = (e = this.param).showCb) || undefined === t || t.call(e, this.node);
    this.node.opacity = 255;
  };
  _ctor.prototype.setInfo = function () {
    $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "contentNode", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_PrePare_Shop"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_PrePare_Shop;