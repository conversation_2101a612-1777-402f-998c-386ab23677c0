var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2Notifier = require("Notifier");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_AutoFollow = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.frame_time = 30;
    t.offset = cc.Vec2.ZERO;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "target", {
    get: function () {
      var e;
      this._target || (this.target = null === (e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetMainRole)) || undefined === e ? undefined : e.node);
      return this._target;
    },
    set: function (e) {
      this._target = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.update = function () {
    if (cc.isValid(this.target)) {
      this.node.position = this.target.position.add(this.offset);
    } else {
      this.destroy();
    }
  };
  cc__decorate([ccp_property({
    displayName: "速度",
    min: 0,
    max: 60,
    slide: true
  })], _ctor.prototype, "frame_time", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/AutoFollow")], _ctor);
}(cc.Component);
exports.default = def_AutoFollow;