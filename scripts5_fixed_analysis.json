{"singleCcclassFiles": [{"fileName": "ArcBullet.js", "className": "ArcBullet", "ccclassScore": 10}, {"fileName": "AutoAmTool.js", "className": "AutoAmTool", "ccclassScore": 16}, {"fileName": "AutoAnimationClip.js", "className": "AutoAnimationClip", "ccclassScore": 17}, {"fileName": "AutoFollow.js", "className": "AutoFollow", "ccclassScore": 15}, {"fileName": "BackHeroProp.js", "className": "BackHeroProp", "ccclassScore": 8}, {"fileName": "BackpackHeroHome.js", "className": "BackpackHeroHome", "ccclassScore": 12}, {"fileName": "BoomerangBullet.js", "className": "BoomerangBullet", "ccclassScore": 10}, {"fileName": "BounceBullet.js", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ccclassScore": 10}, {"fileName": "Buff.js", "className": "Buff", "ccclassScore": 1}, {"fileName": "BuffCardItem.js", "className": "BuffCardItem", "ccclassScore": 14}, {"fileName": "Bullet.js", "className": "Bullet", "ccclassScore": 10}, {"fileName": "BulletBase.js", "className": "BulletBase", "ccclassScore": 13}, {"fileName": "Bullet_Arrow.js", "className": "Bullet_Arrow", "ccclassScore": 10}, {"fileName": "Bullet_FollowTarget.js", "className": "Bullet_FollowTarget", "ccclassScore": 10}, {"fileName": "Bullet_HitReflex.js", "className": "Bullet_HitReflex", "ccclassScore": 10}, {"fileName": "Bullet_Laser.js", "className": "Bullet_Laser", "ccclassScore": 10}, {"fileName": "Bullet_Ligature.js", "className": "Bullet_Ligature", "ccclassScore": 12}, {"fileName": "Bullet_LigaturePonit.js", "className": "Bullet_LigaturePonit", "ccclassScore": 10}, {"fileName": "Bullet_Path.js", "className": "Bullet_Path", "ccclassScore": 10}, {"fileName": "Bullet_RandomMove.js", "className": "Bullet_RandomMove", "ccclassScore": 10}, {"fileName": "Bullet_RigidBody.js", "className": "Bullet_RigidBody", "ccclassScore": 11}, {"fileName": "CircleBullet.js", "className": "CircleBullet", "ccclassScore": 10}, {"fileName": "Commonguide.js", "className": "Commonguide", "ccclassScore": 14}, {"fileName": "ContinuousBullet.js", "className": "Continuous<PERSON>ullet", "ccclassScore": 10}, {"fileName": "DialogBox.js", "className": "DialogBox", "ccclassScore": 12}, {"fileName": "DragonBody.js", "className": "DragonBody", "ccclassScore": 12}, {"fileName": "EffectSkeleton.js", "className": "EffectSkeleton", "ccclassScore": 14}, {"fileName": "Effect_Behead.js", "className": "Effect_Behead", "ccclassScore": 8}, {"fileName": "Effect_Behit.js", "className": "Effect_Behit", "ccclassScore": 8}, {"fileName": "EnergyStamp.js", "className": "EnergyStamp", "ccclassScore": 15}, {"fileName": "EntityDieEffect.js", "className": "EntityDieEffect", "ccclassScore": 10}, {"fileName": "ExchangeCodeView.js", "className": "ExchangeCodeView", "ccclassScore": 14}, {"fileName": "FBoxCollider.js", "className": "FBoxCollider", "ccclassScore": 12}, {"fileName": "FCircleCollider.js", "className": "FCircleCollider", "ccclassScore": 12}, {"fileName": "FColliderManager.js", "className": "FColliderManager", "ccclassScore": 3}, {"fileName": "FPolygonCollider.js", "className": "FPolygonCollider", "ccclassScore": 12}, {"fileName": "GameAnimi.js", "className": "GameAnimi", "ccclassScore": 13}, {"fileName": "GameEffect.js", "className": "GameEffect", "ccclassScore": 8}, {"fileName": "GameSkeleton.js", "className": "GameSkeleton", "ccclassScore": 16}, {"fileName": "GiftPackView.js", "className": "GiftPackView", "ccclassScore": 14}, {"fileName": "Goods.js", "className": "Goods", "ccclassScore": 10}, {"fileName": "GoodsUIItem.js", "className": "GoodsUIItem", "ccclassScore": 17}, {"fileName": "GridViewCell.js", "className": "Grid<PERSON>iew<PERSON>ell", "ccclassScore": 12}, {"fileName": "LaserRadiationBullet.js", "className": "LaserRadiationBullet", "ccclassScore": 10}, {"fileName": "Launcher.js", "className": "Launcher", "ccclassScore": 20}, {"fileName": "LifeBar.js", "className": "LifeBar", "ccclassScore": 10}, {"fileName": "LifeLabel.js", "className": "LifeLabel", "ccclassScore": 10}, {"fileName": "LigatureBullet.js", "className": "LigatureBullet", "ccclassScore": 10}, {"fileName": "M20Equipitem.js", "className": "M20Equipitem", "ccclassScore": 14}, {"fileName": "M20EquipitemBlock.js", "className": "M20EquipitemBlock", "ccclassScore": 10}, {"fileName": "M20EquipitemList.js", "className": "M20EquipitemList", "ccclassScore": 14}, {"fileName": "M20Gooditem.js", "className": "M20Gooditem", "ccclassScore": 12}, {"fileName": "M20Prop.js", "className": "M20Prop", "ccclassScore": 12}, {"fileName": "M20Prop_Equip.js", "className": "M20Prop_Equip", "ccclassScore": 9}, {"fileName": "M20Prop_Gemstone.js", "className": "M20Prop_Gemstone", "ccclassScore": 9}, {"fileName": "M20_PartItem.js", "className": "M20_PartItem", "ccclassScore": 19}, {"fileName": "M20_Pop_EquipInfo.js", "className": "M20_Pop_EquipInfo", "ccclassScore": 14}, {"fileName": "M20_Pop_GameRewardView.js", "className": "M20_Pop_GameRewardView", "ccclassScore": 14}, {"fileName": "M20_Pop_GetBox.js", "className": "M20_Pop_GetBox", "ccclassScore": 14}, {"fileName": "M20_Pop_GetEnergy.js", "className": "M20_Pop_GetEnergy", "ccclassScore": 14}, {"fileName": "M20_Pop_Insufficient_Props_Tips.js", "className": "M20_Pop_Insufficient_Props_Tips", "ccclassScore": 10}, {"fileName": "M20_Pop_NewEquipUnlock.js", "className": "M20_Pop_NewEquipUnlock", "ccclassScore": 12}, {"fileName": "M20_Pop_ShopBoxInfo.js", "className": "M20_Pop_ShopBoxInfo", "ccclassScore": 14}, {"fileName": "M20_Pop_ShopBuyConfirm.js", "className": "M20_Pop_ShopBuyConfirm", "ccclassScore": 14}, {"fileName": "M20_PrePare_Activity.js", "className": "M20_PrePare_Activity", "ccclassScore": 13}, {"fileName": "M20_PrePare_Equip.js", "className": "M20_PrePare_Equip", "ccclassScore": 14}, {"fileName": "M20_PrePare_Fight.js", "className": "M20_PrePare_Fight", "ccclassScore": 14}, {"fileName": "M20_PrePare_MenuView.js", "className": "M20_PrePare_MenuView", "ccclassScore": 14}, {"fileName": "M20_PrePare_Shop.js", "className": "M20_PrePare_Shop", "ccclassScore": 15}, {"fileName": "M20_ShopPartItem.js", "className": "M20_ShopPartItem", "ccclassScore": 16}, {"fileName": "M20_ShopPartItem_adcoupon.js", "className": "M20_ShopPartItem_adcoupon", "ccclassScore": 8}, {"fileName": "M20_ShopPartItem_box.js", "className": "M20_ShopPartItem_box", "ccclassScore": 12}, {"fileName": "M20_ShopPartItem_coin.js", "className": "M20_ShopPartItem_coin", "ccclassScore": 8}, {"fileName": "M20_ShopPartItem_daily.js", "className": "M20_ShopPartItem_daily", "ccclassScore": 13}, {"fileName": "M20_ShopPartItem_hero.js", "className": "M20_ShopPartItem_hero", "ccclassScore": 8}, {"fileName": "M20_Shop_HeroItem.js", "className": "M20_Shop_HeroItem", "ccclassScore": 13}, {"fileName": "M33_FightBuffView.js", "className": "M33_FightBuffView", "ccclassScore": 14}, {"fileName": "M33_Pop_DiffSelectGeneral.js", "className": "M33_Pop_DiffSelectGeneral", "ccclassScore": 10}, {"fileName": "M33_Pop_GameEnd.js", "className": "M33_Pop_GameEnd", "ccclassScore": 12}, {"fileName": "M33_Pop_Revive.js", "className": "M33_Pop_Revive", "ccclassScore": 10}, {"fileName": "M33_TestBox.js", "className": "M33_TestBox", "ccclassScore": 15}, {"fileName": "MBRMonster.js", "className": "MBRMonster", "ccclassScore": 11}, {"fileName": "MBRRole.js", "className": "MBRRole", "ccclassScore": 10}, {"fileName": "MCPet.js", "className": "MCPet", "ccclassScore": 10}, {"fileName": "MCRole.js", "className": "MCRole", "ccclassScore": 10}, {"fileName": "MMGMonster.js", "className": "MMGMonster", "ccclassScore": 14}, {"fileName": "MMGRole.js", "className": "MMGRole", "ccclassScore": 14}, {"fileName": "MonstarTideDragon.js", "className": "MonstarTideDragon", "ccclassScore": 2}, {"fileName": "MonsterElite.js", "className": "MonsterElite", "ccclassScore": 2}, {"fileName": "MonsterTidal.js", "className": "MonsterTidal", "ccclassScore": 2}, {"fileName": "MonsterTidalBoss.js", "className": "MonsterTidalBoss", "ccclassScore": 2}, {"fileName": "MonsterTideDefend.js", "className": "MonsterTideDefend", "ccclassScore": 11}, {"fileName": "MoreGamesItem.js", "className": "MoreGamesItem", "ccclassScore": 14}, {"fileName": "MoveEntity.js", "className": "MoveEntity", "ccclassScore": 10}, {"fileName": "MoveImg.js", "className": "MoveImg", "ccclassScore": 14}, {"fileName": "MovingBGSprite.js", "className": "MovingBGSprite", "ccclassScore": 12}, {"fileName": "MTideDefendRmod.js", "className": "MTideDefendRmod", "ccclassScore": 14}, {"fileName": "MTKRole.js", "className": "MTKRole", "ccclassScore": 10}, {"fileName": "NPC.js", "className": "NPC", "ccclassScore": 8}, {"fileName": "Pet.js", "className": "Pet", "ccclassScore": 8}, {"fileName": "PropertyVo.js", "className": "Property", "ccclassScore": 2}, {"fileName": "RBadgePoint.js", "className": "RBadgePoint", "ccclassScore": 16}, {"fileName": "ReflexBullet.js", "className": "ReflexBullet", "ccclassScore": 10}, {"fileName": "Role.js", "className": "Role", "ccclassScore": 8}, {"fileName": "SelectAlert.js", "className": "Select<PERSON>lert", "ccclassScore": 12}, {"fileName": "ShareButton.js", "className": "ShareButton", "ccclassScore": 20}, {"fileName": "SkeletonBullet.js", "className": "SkeletonBullet", "ccclassScore": 12}, {"fileName": "TestItem.js", "className": "TestItem", "ccclassScore": 12}, {"fileName": "ThrowBullet.js", "className": "ThrowBullet", "ccclassScore": 10}, {"fileName": "TornadoBullet.js", "className": "TornadoBullet", "ccclassScore": 10}, {"fileName": "TrackBullet.js", "className": "TrackBullet", "ccclassScore": 10}, {"fileName": "TrackItem.js", "className": "TrackItem", "ccclassScore": 14}, {"fileName": "TrackManger.js", "className": "TrackManger", "ccclassScore": 1}, {"fileName": "Vehicle.js", "className": "Vehicle", "ccclassScore": 2}, {"fileName": "VideoButton.js", "className": "VideoButton", "ccclassScore": 20}, {"fileName": "VideoIcon.js", "className": "VideoIcon", "ccclassScore": 14}, {"fileName": "VisibleComponent.js", "className": "VisibleComponent", "ccclassScore": 15}, {"fileName": "WallBase.js", "className": "WallBase", "ccclassScore": 8}, {"fileName": "Weather.js", "className": "Weather", "ccclassScore": 10}], "singleNonCcclassFiles": [{"fileName": "ADModel.js", "className": "ADModel", "nonCcclassScore": 0}, {"fileName": "Api.js", "className": "click", "nonCcclassScore": 3}, {"fileName": "BottomBarModel.js", "className": "BottomBarModel", "nonCcclassScore": 0}, {"fileName": "BuffModel.js", "className": "BuffModel", "nonCcclassScore": 0}, {"fileName": "BulletVoPool.js", "className": "BulletVoPool", "nonCcclassScore": 0}, {"fileName": "ByteDance.js", "className": "ByteDance", "nonCcclassScore": 1}, {"fileName": "Cfg.js", "className": "Cfg", "nonCcclassScore": 0}, {"fileName": "CompManager.js", "className": "CompManager", "nonCcclassScore": 0}, {"fileName": "config.js", "className": "API_SECRET", "nonCcclassScore": 2}, {"fileName": "EventModel.js", "className": "EventModel", "nonCcclassScore": 0}, {"fileName": "FightModel.js", "className": "FightModel", "nonCcclassScore": 0}, {"fileName": "GTSimpleSpriteAssembler2D.js", "className": "GTSimpleSpriteAssembler2D", "nonCcclassScore": 0}, {"fileName": "GuidesModel.js", "className": "GuidesModel", "nonCcclassScore": 0}, {"fileName": "index.js", "className": "minSdk", "nonCcclassScore": 2}, {"fileName": "IOSSdk.js", "className": "IOSSdk", "nonCcclassScore": 0}, {"fileName": "ItemModel.js", "className": "ItemModel", "nonCcclassScore": 0}, {"fileName": "JUHEAndroid.js", "className": "JUHEAndroid", "nonCcclassScore": 0}, {"fileName": "KnapsackVo.js", "className": "KnapsackVo", "nonCcclassScore": 0}, {"fileName": "LatticeMap.js", "className": "LatticeMap", "nonCcclassScore": 0}, {"fileName": "LevelMgr.js", "className": "Level", "nonCcclassScore": 0}, {"fileName": "LoadingModel.js", "className": "LoadingModel", "nonCcclassScore": 0}, {"fileName": "LocalStorage.js", "className": "LocalStorage", "nonCcclassScore": 0}, {"fileName": "MBackpackHero.js", "className": "MBPack", "nonCcclassScore": 0}, {"fileName": "MBRebound.js", "className": "MBRebound", "nonCcclassScore": 0}, {"fileName": "MCBossState.js", "className": "MCBossState", "nonCcclassScore": 0}, {"fileName": "MChains.js", "className": "<PERSON><PERSON><PERSON>", "nonCcclassScore": 0}, {"fileName": "MMGuards.js", "className": "<PERSON><PERSON><PERSON><PERSON>", "nonCcclassScore": 0}, {"fileName": "ModeAllOutAttackModel.js", "className": "ModeAllOutAttackModel", "nonCcclassScore": 0}, {"fileName": "ModeBulletsReboundModel.js", "className": "ModeBulletsReboundModel", "nonCcclassScore": 0}, {"fileName": "ModeChainsModel.js", "className": "ModeChainsModel", "nonCcclassScore": 0}, {"fileName": "ModeDragonWarModel.js", "className": "ModeDragonWarModel", "nonCcclassScore": 0}, {"fileName": "ModeManGuardsModel.js", "className": "ModeManGuardsModel", "nonCcclassScore": 0}, {"fileName": "ModePickUpBulletsModel.js", "className": "ModePickUpBulletsModel", "nonCcclassScore": 0}, {"fileName": "ModeThrowingKnifeModel.js", "className": "ModeThrowingKnifeModel", "nonCcclassScore": 0}, {"fileName": "ModuleLauncher.js", "className": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "nonCcclassScore": 1}, {"fileName": "MonsterState.js", "className": "MonsterState", "nonCcclassScore": 0}, {"fileName": "MonsterTidalState.js", "className": "MonsterTidalState", "nonCcclassScore": 0}, {"fileName": "MovingBGAssembler.js", "className": "MovingBGAssembler", "nonCcclassScore": 0}, {"fileName": "MTideDefendRebound.js", "className": "MTideDefendRebound", "nonCcclassScore": 0}, {"fileName": "MTKnife.js", "className": "MTKnife", "nonCcclassScore": 0}, {"fileName": "NativeAndroid.js", "className": "NativeAndroid", "nonCcclassScore": 0}, {"fileName": "PayModel.js", "className": "PayModel", "nonCcclassScore": 0}, {"fileName": "PetState.js", "className": "PetState", "nonCcclassScore": 0}, {"fileName": "RewardEvent.js", "className": "RewardEvent", "nonCcclassScore": 0}, {"fileName": "RoleState.js", "className": "RoleState", "nonCcclassScore": 0}, {"fileName": "SettingModel.js", "className": "SettingModel", "nonCcclassScore": 0}, {"fileName": "ShopModel.js", "className": "ShopModel", "nonCcclassScore": 0}, {"fileName": "SkillManager.js", "className": "Skill", "nonCcclassScore": 0}, {"fileName": "SkillModel.js", "className": "SkillModel", "nonCcclassScore": 0}, {"fileName": "TestModel.js", "className": "TestModel", "nonCcclassScore": 0}, {"fileName": "TideDefendModel.js", "className": "TideDefendModel", "nonCcclassScore": 0}, {"fileName": "ttPostbackCtl.js", "className": "ttPostbackCtl", "nonCcclassScore": 1}, {"fileName": "UILauncher.js", "className": "UILauncher", "nonCcclassScore": 1}, {"fileName": "WebDev.js", "className": "WebDev", "nonCcclassScore": 0}], "multiDefinitionFiles": [{"fileName": "ADController.js", "totalDefinitions": 2}, {"fileName": "AlertManager.js", "totalDefinitions": 2}, {"fileName": "AssetLoader.js", "totalDefinitions": 3}, {"fileName": "AutoScaleComponent.js", "totalDefinitions": 2}, {"fileName": "BagModeSkillPoolCfg.js", "totalDefinitions": 2}, {"fileName": "bagMonsterLvCfg.js", "totalDefinitions": 2}, {"fileName": "BagShopItemCfg.js", "totalDefinitions": 2}, {"fileName": "BagSkillCfg.js", "totalDefinitions": 2}, {"fileName": "BottomBarController.js", "totalDefinitions": 2}, {"fileName": "BottomBarView.js", "totalDefinitions": 2}, {"fileName": "BoxLevelExpCfg.js", "totalDefinitions": 2}, {"fileName": "BronMonsterManger.js", "totalDefinitions": 2}, {"fileName": "BuffCfg.js", "totalDefinitions": 2}, {"fileName": "BuffController.js", "totalDefinitions": 2}, {"fileName": "BuffList.js", "totalDefinitions": 36}, {"fileName": "BuildModeSkiilpoolCfg.js", "totalDefinitions": 2}, {"fileName": "BulletEffectCfg.js", "totalDefinitions": 2}, {"fileName": "CurrencyConfigCfg.js", "totalDefinitions": 2}, {"fileName": "dmmItemCfg.js", "totalDefinitions": 2}, {"fileName": "dmmRoleCfg.js", "totalDefinitions": 2}, {"fileName": "Dragon.js", "totalDefinitions": 2}, {"fileName": "dragonPathCfg.js", "totalDefinitions": 2}, {"fileName": "DropConfigCfg.js", "totalDefinitions": 2}, {"fileName": "EaseScaleTransition.js", "totalDefinitions": 2}, {"fileName": "EquipLvCfg.js", "totalDefinitions": 2}, {"fileName": "EquipMergeLvCfg.js", "totalDefinitions": 2}, {"fileName": "EventController.js", "totalDefinitions": 2}, {"fileName": "FCollider.js", "totalDefinitions": 2}, {"fileName": "FightController.js", "totalDefinitions": 2}, {"fileName": "FightScene.js", "totalDefinitions": 2}, {"fileName": "FightUIView.js", "totalDefinitions": 2}, {"fileName": "Game.js", "totalDefinitions": 2}, {"fileName": "GameatrCfg.js", "totalDefinitions": 2}, {"fileName": "GameCamera.js", "totalDefinitions": 2}, {"fileName": "GameSettingCfg.js", "totalDefinitions": 2}, {"fileName": "GridView.js", "totalDefinitions": 2}, {"fileName": "GridViewFreshWork.js", "totalDefinitions": 3}, {"fileName": "GuideCfg.js", "totalDefinitions": 2}, {"fileName": "GuidesController.js", "totalDefinitions": 2}, {"fileName": "ItemController.js", "totalDefinitions": 2}, {"fileName": "languageCfg.js", "totalDefinitions": 2}, {"fileName": "LanguageFun.js", "totalDefinitions": 2}, {"fileName": "LevelExpCfg.js", "totalDefinitions": 2}, {"fileName": "LoadingController.js", "totalDefinitions": 2}, {"fileName": "LoadingView.js", "totalDefinitions": 2}, {"fileName": "LvInsideCfg.js", "totalDefinitions": 2}, {"fileName": "LvOutsideCfg.js", "totalDefinitions": 2}, {"fileName": "M33_FightScene.js", "totalDefinitions": 2}, {"fileName": "M33_FightUIView.js", "totalDefinitions": 2}, {"fileName": "MapCfg.js", "totalDefinitions": 2}, {"fileName": "MCBoss.js", "totalDefinitions": 2}, {"fileName": "MCDragoMutilation.js", "totalDefinitions": 2}, {"fileName": "MCDragon.js", "totalDefinitions": 2}, {"fileName": "MiniGameEquipCfg.js", "totalDefinitions": 2}, {"fileName": "MiniGameLvCfg.js", "totalDefinitions": 2}, {"fileName": "ModeAllOutAttackController.js", "totalDefinitions": 2}, {"fileName": "ModeBackpackHeroController.js", "totalDefinitions": 2}, {"fileName": "ModeBackpackHeroModel.js", "totalDefinitions": 2}, {"fileName": "ModeBulletsReboundController.js", "totalDefinitions": 2}, {"fileName": "ModeChainsController.js", "totalDefinitions": 2}, {"fileName": "ModeDragonWarController.js", "totalDefinitions": 2}, {"fileName": "ModeManGuardsController.js", "totalDefinitions": 2}, {"fileName": "ModePickUpBulletsController.js", "totalDefinitions": 2}, {"fileName": "ModeThrowingKnifeController.js", "totalDefinitions": 2}, {"fileName": "Monster.js", "totalDefinitions": 2}, {"fileName": "MonsterCfg.js", "totalDefinitions": 2}, {"fileName": "MonsterLvCfg.js", "totalDefinitions": 2}, {"fileName": "MoreGamesView.js", "totalDefinitions": 2}, {"fileName": "NotifyCaller.js", "totalDefinitions": 2}, {"fileName": "NotifyListener.js", "totalDefinitions": 3}, {"fileName": "OrganismBase.js", "totalDefinitions": 2}, {"fileName": "PayController.js", "totalDefinitions": 2}, {"fileName": "PayShopCfg.js", "totalDefinitions": 2}, {"fileName": "PoolListCfg.js", "totalDefinitions": 2}, {"fileName": "Pop.js", "totalDefinitions": 2}, {"fileName": "ProcessRewardsCfg.js", "totalDefinitions": 2}, {"fileName": "randomNameCfg.js", "totalDefinitions": 2}, {"fileName": "RBadgeController.js", "totalDefinitions": 2}, {"fileName": "RBadgeModel.js", "totalDefinitions": 2}, {"fileName": "ResUtil.js", "totalDefinitions": 2}, {"fileName": "RoleCfg.js", "totalDefinitions": 2}, {"fileName": "RoleLvCfg.js", "totalDefinitions": 2}, {"fileName": "RoleSkillList.js", "totalDefinitions": 36}, {"fileName": "RoleUnlockCfg.js", "totalDefinitions": 2}, {"fileName": "SdkLauncher.js", "totalDefinitions": 2}, {"fileName": "SettingController.js", "totalDefinitions": 2}, {"fileName": "SettingView.js", "totalDefinitions": 2}, {"fileName": "ShopController.js", "totalDefinitions": 2}, {"fileName": "signCfg.js", "totalDefinitions": 2}, {"fileName": "SkiilpoolCfg.js", "totalDefinitions": 2}, {"fileName": "SkillCfg.js", "totalDefinitions": 2}, {"fileName": "SkillController.js", "totalDefinitions": 2}, {"fileName": "SkillModule.js", "totalDefinitions": 2}, {"fileName": "SoundCfg.js", "totalDefinitions": 2}, {"fileName": "TaskCfg.js", "totalDefinitions": 2}, {"fileName": "TaskModel.js", "totalDefinitions": 2}, {"fileName": "TaskTypeCfg.js", "totalDefinitions": 2}, {"fileName": "TestController.js", "totalDefinitions": 2}, {"fileName": "TestView.js", "totalDefinitions": 2}, {"fileName": "TideDefendController.js", "totalDefinitions": 2}, {"fileName": "TowerAmethystRewardCfg.js", "totalDefinitions": 2}, {"fileName": "TowerCfg.js", "totalDefinitions": 2}, {"fileName": "TowerCoinRewardCfg.js", "totalDefinitions": 2}, {"fileName": "TowerLvCfg.js", "totalDefinitions": 2}, {"fileName": "TowerMenuCfg.js", "totalDefinitions": 2}, {"fileName": "TwoDHorizontalLayoutObject.js", "totalDefinitions": 2}, {"fileName": "TwoDLayoutObject.js", "totalDefinitions": 2}, {"fileName": "UserVo.js", "totalDefinitions": 3}, {"fileName": "WeatherCfg.js", "totalDefinitions": 2}, {"fileName": "WonderSdk.js", "totalDefinitions": 2}], "noDefinitionFiles": [{"fileName": "Report.js"}, {"fileName": "ReportQueue.js"}, {"fileName": "TimeManage.js"}]}