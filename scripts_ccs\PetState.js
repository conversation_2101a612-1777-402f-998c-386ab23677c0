var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PetState = undefined;
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var u = cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
(function (e) {
  var t = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.IDLE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("idle", true);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.IdleState = t;
  var o = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._patrolAngle = cc.Vec2.ZERO;
      t._patrolPos = cc.Vec2.ZERO;
      t._time = 0;
      t._nextTiem = 2;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.WANDER;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      this.changeAngle();
    };
    t.prototype.changeAngle = function () {
      this._time = 0;
      if (this._owner.targetGap > this._owner.followGap) {
        this._owner.toFollow();
      } else {
        this._patrolPos = $2GameUtil.GameUtil.AngleAndLenToPos($2GameUtil.GameUtil.random(0, 360), $2GameUtil.GameUtil.random(200, 1.5 * this._owner.followGap));
        this._patrolAngle = this._patrolPos.normalize();
        this._nextTiem = $2GameUtil.GameUtil.random(1, 4);
      }
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      cc.Vec2.squaredDistance(t.steering.curTarget, t.position) < Math.pow(50, 2) || this.onSteerMove(t, o);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.WanderState = o;
  var i = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.FOLLOW;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
    };
    t.prototype.onUpdate = function (t, o) {
      if (!this._owner.isBanMove) {
        e.prototype.onUpdate.call(this, t, o);
        0 != this._owner.maxSpeed && this.onSteerMove(t, o);
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.FollowState = i;
  var p = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._curattackDelta = 0;
      t._attackPoint = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.ATTACK;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      this._curattackDelta = 0;
      this._attackPoint = .5;
      this._owner.isBanMove;
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      this._curattackDelta += o;
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.AttackState = p;
  var f = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.DEAD;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.node.emit($2ListenID.ListenID.Fight_Dead);
      this._deltaTime = .2;
      t.isActive = false;
      $2Game.Game.Mgr.instance.showEntityDieEffect(2, {
        position: t.position,
        scale: 1
      });
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      if (this._deltaTime >= 0 && !t.isDead) {
        this._deltaTime -= o;
        cc.Vec2.set(u, -t.heading.x, -t.heading.y);
        cc.Vec2.multiplyScalar(u, u, 50 * o);
        cc.Vec2.add(u, u, t.position);
        t.setPosition(u);
        if (this._deltaTime <= 0) {
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_PetDead, t), t.isDead = true;
        }
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.DeadState = f;
})(exports.PetState || (exports.PetState = {}));