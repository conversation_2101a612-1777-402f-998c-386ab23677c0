var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Game = require("Game");
var $2BaseSdk = require("BaseSdk");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_ShareButton = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.clickEvents = [];
    t.shareScene = "";
    t.channel = "invite";
    t.isAm = true;
    t.desc = "";
    t.interactable = true;
    t._scale = 1;
    t.isClick = false;
    t.isCanClick = true;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "scene", {
    get: function () {
      var e;
      var t;
      this._scene || (this._scene = this.shareScene || (null === (e = this._baseView) || undefined === e ? undefined : e.eventScene) || (null === (t = this.node) || undefined === t ? undefined : t.name) || "notDefine");
      return this._scene;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    this._scale = this.node.scale;
    this._baseView = this.getBaseView;
    this.node.getORaddComponent(cc.BlockInputEvents);
  };
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.start = function () {
    this.sendEvent("show");
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (e) {
    this.node.changeListener(e, cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
    this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
  };
  _ctor.prototype.onTouchStart = function () {
    var e = this;
    if (this.interactable) {
      $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.button_click);
      this.isClick = true;
      cc.Tween.stopAllByTarget(this.node);
      cc.tween(this.node).to(.1, {
        scale: .9 * this._scale
      }).start();
      this.scheduleOnce(function () {
        e.isClick = false;
      }, 1);
    }
  };
  _ctor.prototype.onTouchEnd = function (e) {
    if (this.interactable) {
      this.isClick && this.share(e);
      this.repeatForeverAm();
    }
  };
  _ctor.prototype.share = function (e) {
    var t = this;
    if (this.isCanClick) {
      this.sendEvent("click");
      wonderSdk.share($2BaseSdk.ShareType.SHARE_REWARD, {
        channel: this.channel
      }, function () {
        t.sendEvent("success");
        for (var o = 0; o < t.clickEvents.length; o++) {
          var i = t.clickEvents[o];
          i.emit([e, i.customEventData]);
        }
        t.onSuccess();
      });
    }
  };
  _ctor.prototype.setCanClick = function (e) {
    this.isCanClick = e;
  };
  _ctor.prototype.repeatForeverAm = function () {
    var e = this;
    cc.Tween.stopAllByTarget(this.node);
    cc.tween(this.node).to(.1, {
      scale: this._scale
    }).call(function () {
      e.isCanClick && e.isAm && cc.tween(e.node).sequence(cc.tween().to(.3, {
        scale: e._scale + .1
      }, {
        easing: "sineInOut"
      }), cc.tween().to(.3, {
        scale: e._scale
      }, {
        easing: "sineInOut"
      })).repeatForever().start();
    }).start();
  };
  _ctor.prototype.onSuccess = function () {
    if (this.scene.includes("PassShareReward")) {
      $2Manager.Manager.vo.userVo.dailyData.TT_PassShare--;
      $2Manager.Manager.vo.saveUserData();
    }
    if (this.scene.includes("ShareGiftView")) {
      $2Manager.Manager.vo.userVo.dailyData.TT_ShareGiftView--;
      $2Manager.Manager.vo.saveUserData();
    }
  };
  _ctor.prototype.sendEvent = function (e) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "share_btn", cc__assign(cc__assign({
      Type: e,
      Scene: this.scene
    }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}), this.eventPram || {}));
  };
  Object.defineProperty(_ctor.prototype, "getBaseView", {
    get: function () {
      var e;
      for (var t = this.node; !e && t && t.parent;) {
        var o = t.parent.getComponent($2MVC.MVC.BaseView);
        if (o) {
          e = o;
        } else {
          t.parent && (t = t.parent);
        }
      }
      return e;
    },
    enumerable: false,
    configurable: true
  });
  cc__decorate([ccp_property([cc.Component.EventHandler])], _ctor.prototype, "clickEvents", undefined);
  cc__decorate([ccp_property({
    displayName: "分享场景(默认界面名称)"
  })], _ctor.prototype, "shareScene", undefined);
  cc__decorate([ccp_property({
    displayName: "分享频道"
  })], _ctor.prototype, "channel", undefined);
  cc__decorate([ccp_property({
    displayName: "是否有动画"
  })], _ctor.prototype, "isAm", undefined);
  cc__decorate([ccp_property({
    displayName: "分享提示描述"
  })], _ctor.prototype, "desc", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/ShareButton")], _ctor);
}(cc.Component);
exports.default = def_ShareButton;