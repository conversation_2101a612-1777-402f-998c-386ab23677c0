{"timestamp": "2025-07-09T03:02:52.485Z", "sourceDir": "./scripts_5", "targetDir": "./scripts_ccs", "totalFiles": 33, "successCount": 33, "failCount": 0, "movedFiles": [{"fileName": "Api.js", "className": "click", "isPossibleCcclass": false}, {"fileName": "Buff.js", "className": "Buff", "isPossibleCcclass": false}, {"fileName": "BulletVoPool.js", "className": "BulletVoPool", "isPossibleCcclass": false}, {"fileName": "Cfg.js", "className": "Cfg", "isPossibleCcclass": false}, {"fileName": "config.js", "className": "API_SECRET", "isPossibleCcclass": false}, {"fileName": "FCollider.js", "className": "ColliderType", "isPossibleCcclass": false}, {"fileName": "GridView.js", "className": "GRID_TYPE", "isPossibleCcclass": false}, {"fileName": "index.js", "className": "minSdk", "isPossibleCcclass": false}, {"fileName": "KnapsackVo.js", "className": "KnapsackVo", "isPossibleCcclass": false}, {"fileName": "LatticeMap.js", "className": "LatticeMap", "isPossibleCcclass": false}, {"fileName": "LevelMgr.js", "className": "Level", "isPossibleCcclass": false}, {"fileName": "MBackpackHero.js", "className": "MBPack", "isPossibleCcclass": false}, {"fileName": "MBRebound.js", "className": "MBRebound", "isPossibleCcclass": false}, {"fileName": "MCBossState.js", "className": "MCBossState", "isPossibleCcclass": false}, {"fileName": "MChains.js", "className": "<PERSON><PERSON><PERSON>", "isPossibleCcclass": false}, {"fileName": "MMGuards.js", "className": "<PERSON><PERSON><PERSON><PERSON>", "isPossibleCcclass": false}, {"fileName": "ModeBackpackHeroModel.js", "className": "ActivityPass", "isPossibleCcclass": false}, {"fileName": "ModuleLauncher.js", "className": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "isPossibleCcclass": false}, {"fileName": "MonsterState.js", "className": "MonsterState", "isPossibleCcclass": false}, {"fileName": "MonsterTidalState.js", "className": "MonsterTidalState", "isPossibleCcclass": false}, {"fileName": "MoreGamesView.js", "className": "MoreGames", "isPossibleCcclass": false}, {"fileName": "MTideDefendRebound.js", "className": "MTideDefendRebound", "isPossibleCcclass": false}, {"fileName": "MTKnife.js", "className": "MTKnife", "isPossibleCcclass": false}, {"fileName": "OrganismBase.js", "className": "nullMap", "isPossibleCcclass": false}, {"fileName": "PetState.js", "className": "PetState", "isPossibleCcclass": false}, {"fileName": "PropertyVo.js", "className": "Property", "isPossibleCcclass": false}, {"fileName": "RBadgeModel.js", "className": "RBadge", "isPossibleCcclass": false}, {"fileName": "RewardEvent.js", "className": "RewardEvent", "isPossibleCcclass": false}, {"fileName": "RoleState.js", "className": "RoleState", "isPossibleCcclass": false}, {"fileName": "SkillManager.js", "className": "Skill", "isPossibleCcclass": false}, {"fileName": "TaskModel.js", "className": "TaskSaveType", "isPossibleCcclass": false}, {"fileName": "TrackManger.js", "className": "TrackManger", "isPossibleCcclass": false}, {"fileName": "UILauncher.js", "className": "UILauncher", "isPossibleCcclass": false}]}