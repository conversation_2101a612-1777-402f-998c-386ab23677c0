const fs = require('fs');
const path = require('path');

// 读取分析结果
function loadAnalysisResult() {
    try {
        const data = fs.readFileSync('ccclass_analysis_result.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('无法读取分析结果文件:', error.message);
        return null;
    }
}

// 创建目标文件夹
function createTargetDirectory(targetDir) {
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        console.log(`创建目标文件夹: ${targetDir}`);
    } else {
        console.log(`目标文件夹已存在: ${targetDir}`);
    }
}

// 复制文件
function copyFile(sourcePath, targetPath) {
    try {
        fs.copyFileSync(sourcePath, targetPath);
        return true;
    } catch (error) {
        console.error(`复制文件失败: ${sourcePath} -> ${targetPath}`, error.message);
        return false;
    }
}

// 主函数
function main() {
    const analysisResult = loadAnalysisResult();
    if (!analysisResult) {
        return;
    }

    const sourceDir = './scripts_5';
    const targetDir = './scripts_ccs';
    
    // 创建目标文件夹
    createTargetDirectory(targetDir);
    
    const singleClassFiles = analysisResult.singleClassFiles;
    
    console.log(`\n开始移动 ${singleClassFiles.length} 个单类文件到 ${targetDir}...\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    singleClassFiles.forEach((fileInfo, index) => {
        const fileName = fileInfo.fileName;
        const sourcePath = path.join(sourceDir, fileName);
        const targetPath = path.join(targetDir, fileName);
        
        console.log(`[${index + 1}/${singleClassFiles.length}] 复制: ${fileName} (${fileInfo.className})`);
        
        if (fs.existsSync(sourcePath)) {
            if (copyFile(sourcePath, targetPath)) {
                successCount++;
                console.log(`  ✓ 成功复制到: ${targetPath}`);
            } else {
                failCount++;
                console.log(`  ✗ 复制失败`);
            }
        } else {
            failCount++;
            console.log(`  ✗ 源文件不存在: ${sourcePath}`);
        }
    });
    
    console.log(`\n=== 移动完成 ===`);
    console.log(`成功: ${successCount} 个文件`);
    console.log(`失败: ${failCount} 个文件`);
    console.log(`目标文件夹: ${path.resolve(targetDir)}`);
    
    // 生成移动报告
    const report = {
        timestamp: new Date().toISOString(),
        sourceDir: sourceDir,
        targetDir: targetDir,
        totalFiles: singleClassFiles.length,
        successCount: successCount,
        failCount: failCount,
        movedFiles: singleClassFiles.map(f => ({
            fileName: f.fileName,
            className: f.className,
            isPossibleCcclass: f.isPossibleCcclass
        }))
    };
    
    fs.writeFileSync('move_report.json', JSON.stringify(report, null, 2));
    console.log(`\n移动报告已保存到: move_report.json`);
    
    // 显示移动的文件列表
    console.log(`\n=== 已移动的单类文件 ===`);
    singleClassFiles.forEach(fileInfo => {
        const typeInfo = fileInfo.isPossibleCcclass ? '[ccclass]' : '[class]';
        console.log(`${fileInfo.fileName} - ${fileInfo.className} ${typeInfo}`);
    });
}

main();
