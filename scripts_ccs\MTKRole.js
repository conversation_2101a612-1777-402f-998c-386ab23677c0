var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2MTKnife = require("MTKnife");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
cc.v2();
cc.v2();
var def_MTKRole = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.lbl_dynamics = null;
    t.lbl_angle = null;
    t._myData = null;
    t.roleId = 3e4;
    t.touchPos = cc.v2();
    t.bulletIndex = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.uiSpine, this.mySkeleton.node).then(function (e) {
          o.mySkeleton.reset(e);
          o.setAnimation("idle", true);
          o.delayByGame(function () {
            o.onNewSize(o.roleNode.getContentSize());
          });
        })));
      }
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.mulSelf(.7);
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
    this.bulletIndex = 0;
    this.knifeController = null;
    this.knifeController = new $2MTKnife.MTKnife.KnifeController().setAttribute({
      ower: this,
      line: this.node.getComByChild(cc.Sprite, "line")
    });
  };
  _ctor.prototype.setRole = function () {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    var e = $2Cfg.Cfg.Role.find({
      roleId: this.roleId
    });
    this.property.set(e);
    this.updateProperty();
    this.skillMgr.add(this.myData.startSkill, false);
    this.initHp();
  };
  _ctor.prototype.changeRole = function (e) {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
  };
  _ctor.prototype.updateProperty = function () {
    if (this.property) {
      e.prototype.updateProperty.call(this);
      this.knifeController.line.node.scale = 1 / this.node.scale;
    }
  };
  _ctor.prototype.behit = function (e) {
    var t = this;
    this.isDead || this.hurtMgr.checkHurt(e) && (this.game.hitBackNum += 1, this.curHp -= e.val, this.node.emit($2ListenID.ListenID.Fight_BeHit, e), this.materialTwinkle(), cc.tween(this.node).parallel(cc.tween().to(.1, {
      angle: 36
    }), cc.tween().bezierTo(.4, this.position, this.position.add(cc.v2(-200, 200)), this.position.add(cc.v2(-200, 0)))).call(function () {
      t.node.angle = 0;
      t.game.showDamageDisplay(e, t.haedPosition);
      if (t.curHp <= 0) {
        t.toDead();
        wonderSdk.vibrate(0);
      }
    }).start());
  };
  _ctor.prototype.materialTwinkle = function () {};
  _ctor.prototype.toDead = function () {
    if (!this.isDead) {
      this.game.showEntityDieEffect(2, {
        position: this.position.clone(),
        scale: 1
      });
      this.isDead = true;
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
    }
  };
  Object.defineProperty(_ctor.prototype, "bulletID", {
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onTouchMove = function (e) {
    if (!this.isDead && this.game.bronMonsterMgr.cutStaus == $2MTKnife.MTKnife.RoundStatus.MyRound) {
      var t = cc.Vec2.distance(e.getStartLocation(), e.getLocation());
      var o = ($2GameUtil.GameUtil.GetAngle(e.getStartLocation(), e.getLocation()) + 90) % 360;
      this.lbl_angle.string = Math.round(o - 90) + "°";
      var i = cc.misc.clampf(t, 10, $2MTKnife.MTKnife.maxPower);
      this.lbl_dynamics.string = Math.round(i / $2MTKnife.MTKnife.maxPower * 100) + "%";
      this.knifeController.aimData = {
        angle: o,
        power: t
      };
    }
  };
  _ctor.prototype.onTouchEnd = function () {
    var e = this;
    if (!this.isDead && this.game.bronMonsterMgr.cutStaus == $2MTKnife.MTKnife.RoundStatus.MyRound && this.knifeController.aimData) {
      this.lbl_angle.string = "0°";
      this.lbl_dynamics.string = "0%";
      this.knifeController.fire(function (t) {
        t.bulletId = e.bulletID;
        var o = $2Cfg.Cfg.Role.find({
          roleId: e.roleId
        });
        t.speed = o.speed;
        t.hurt.baseVal = o.atk;
      }, 0);
    }
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "lbl_dynamics", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "lbl_angle", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}($2OrganismBase.default);
exports.default = def_MTKRole;