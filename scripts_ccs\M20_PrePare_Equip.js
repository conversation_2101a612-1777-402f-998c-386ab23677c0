var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var s;
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2M20EquipitemBlock = require("M20EquipitemBlock");
var $2M20EquipitemList = require("M20EquipitemList");
(function (e) {
  e[e.Start = 1] = "Start";
  e[e.Equip = 2] = "Equip";
})(s || (s = {}));
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_PrePare_Equip = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.equipBox = null;
    t.unlockBox = null;
    t.notUnlockBox = null;
    t.equipGridItem = null;
    t.equiplockitem = null;
    t.stateType = s.Start;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "equippack", {
    get: function () {
      return this.mode.userEquipPack.filter(function (e) {
        return e.isFitOut;
      }).sort(function (e, t) {
        return e.sort - t.sort;
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetAll, this, -200);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_List, this.resetAll, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.M20_SelectEquip, this.getThis, this);
  };
  _ctor.prototype.getThis = function () {
    return this;
  };
  _ctor.prototype.setInfo = function () {
    this.stateType = s.Start;
    var e = cc.find("bg/usingnode/bgBox", this.node);
    for (var t = 0; t < 8; t++) {
      e.children[t] || cc.instantiate(e.children[0]).setAttribute({
        parent: e
      });
    }
    this.resetAll();
  };
  _ctor.prototype.resetAll = function () {
    var e = this;
    this.resetEquip();
    this.resetUnlock();
    this.resetNotUnlock();
    if (11 == $2Manager.Manager.vo.userVo.guideIndex) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
      this.scheduleOnce(function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
          targetNode: e.equipBox.children[0]
        });
      }, 0);
    }
  };
  _ctor.prototype.onClickItem = function (e) {
    if (this.stateType == s.Equip && e.isEquip) {
      this.mode.userEquipPack.replace(e.equipID, this.cutSelectEquip.equipID);
      this.stateType = s.Start;
      $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
    } else if (this.stateType == s.Start && (cc__spreadArrays(this.equipBox.children, this.unlockBox.children).forEach(function (t) {
      var o = t.getComponent($2M20EquipitemList.default);
      o != e && o.resetState();
    }), 12 == $2Manager.Manager.vo.userVo.guideIndex || 16 == $2Manager.Manager.vo.userVo.guideIndex)) {
      var t = 12 == $2Manager.Manager.vo.userVo.guideIndex ? e.dropNode.children[0] : e.btnuse;
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: t,
        offset: cc.v2(0, 10)
      });
    }
  };
  _ctor.prototype.onSelectItem = function (e) {
    16 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
    if (this.equippack.length < 8) {
      this.mode.AssembleEquip(e.equipID);
      $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
    } else {
      this.cutSelectEquip = e;
      this.stateType = s.Equip;
      $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_Item, e);
    }
  };
  _ctor.prototype.resetEquip = function () {
    var e = this;
    this.equipBox.hideAllChildren();
    var t = this.equipBox.getComponent(cc.Layout);
    t.enabled = true;
    this.equippack.forEach(function (t, o) {
      var i = e.equipBox.children[o] || cc.instantiate(e.equipGridItem);
      i.setAttribute({
        parent: e.equipBox,
        active: true,
        zIndex: o
      });
      var n = i.getComponent($2M20EquipitemList.default);
      n.setInfo(+t.id);
      n.setClickCall(e.onClickItem.bind(e));
    });
    this.scheduleOnce(function () {
      t && (t.enabled = false);
    });
  };
  _ctor.prototype.resetUnlock = function () {
    var e = this;
    var t = this.unlockBox.getComponent(cc.Layout);
    t.enabled = true;
    this.unlockBox.hideAllChildren();
    var o = this.mode.userEquipPack.filter(function (e) {
      return !e.isFitOut;
    }).map(function (e) {
      return $2Cfg.Cfg.RoleUnlock.get(e.id);
    });
    o.sort(function (e, t) {
      return t.Count - e.Count;
    });
    o.forEach(function (t, o) {
      var i = e.unlockBox.children[o] || cc.instantiate(e.equipGridItem);
      i.setAttribute({
        parent: e.unlockBox,
        active: true,
        zIndex: o
      });
      var n = i.getComponent($2M20EquipitemList.default);
      n.setInfo(+t.id);
      n.setClickCall(e.onClickItem.bind(e));
    });
    this.scheduleOnce(function () {
      t.enabled = false;
    });
  };
  _ctor.prototype.resetNotUnlock = function () {
    var e = this;
    this.notUnlockBox.hideAllChildren();
    var t = $2Cfg.Cfg.RoleUnlock.getArray().filter(function (t) {
      return 1 == t.type && t.unlock && !e.mode.checkIsUnlock(t);
    });
    t.sort(function (e, t) {
      if (e.rarity == $2GameSeting.GameSeting.RarityType.S || t.rarity == $2GameSeting.GameSeting.RarityType.S) {
        return -1;
      } else {
        return e.Count - t.Count;
      }
    });
    t.forEach(function (t, o) {
      var i = e.notUnlockBox.children[o] || cc.instantiate(e.equiplockitem);
      i.setAttribute({
        parent: e.notUnlockBox,
        active: true
      });
      i.getComponent($2M20EquipitemBlock.default).setInfo(t.id);
    });
  };
  _ctor.prototype.onShowFinish = function () {
    var e;
    var t;
    null === (t = null === (e = this.param) || undefined === e ? undefined : e.showCb) || undefined === t || t.call(e, this.node);
    this.node.opacity = 255;
  };
  _ctor.prototype.onOpen = function () {
    this.node.opacity = 0;
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "equipBox", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "unlockBox", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "notUnlockBox", undefined);
  cc__decorate([ccp_property(cc.Prefab)], _ctor.prototype, "equipGridItem", undefined);
  cc__decorate([ccp_property(cc.Prefab)], _ctor.prototype, "equiplockitem", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_PrePare_Equip"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_PrePare_Equip;