var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Manager = require("Manager");
var $2Monster = require("Monster");
cc.v2();
cc.v2();
var c = cc._decorator.ccclass;
var def_MonsterElite = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this._mals.setProperty("outlineWidth", [2].includes(this.monCfg.type) ? .03 * $2Manager.Manager.vo.switchVo.shardAdapter : 0);
  };
  _ctor.prototype.isOffScreen = function () {
    return false;
  };
  _ctor.prototype.materialTwinkle = function () {
    var e = this;
    if (this._mals) {
      var t = .5;
      this._mals.setProperty("setwhite", t);
      this.schedule(function () {
        e.isValid && e._mals.setProperty("setwhite", Math.max(0, t -= .05));
      }, 0, 30);
    }
  };
  return cc__decorate([c], _ctor);
}($2Monster.Monster);
exports.default = def_MonsterElite;