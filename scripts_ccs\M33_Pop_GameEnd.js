var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Pop = require("Pop");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2ItemModel = require("ItemModel");
var $2MoreGamesView = require("MoreGamesView");
var $2ModeChainsModel = require("ModeChainsModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M33_Pop_GameEnd = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.imgArr = [];
    t.rewardList = [];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    var e;
    var t;
    var o = this;
    var i = this.param.isWin;
    var n = this.param.cfg || this.game.miniGameCfg;
    var r = n.type >= 10;
    var a = i ? 1 : this.game.killMonsterNum / this.game.totalLen;
    var c = Math.floor(100 * a);
    this.mode.levelVo.setLvMaxRound(n.lvid, c, i);

    // $2Manager.Manager.loader.loadSpriteToSprit("img/common/" + (i ? "img_slbt" : "img_sbbt"), this.imgArr[0]);
    // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeChains/ui/" + (i ? "img_sl" : "img_sb"), this.imgArr[1]);

    if (i) {
      this.imgArr[0].node.setActive(true);
      this.imgArr[1].node.setActive(false);
    } else {
      this.imgArr[0].node.setActive(false);
      this.imgArr[1].node.setActive(true);
    }

    var l = this.nextCfg = $2Cfg.Cfg.BagModeLv.get(this.mode.levelVo.CurChallengeLv);
    this.nodeArr[4].hideAllChildren();
    this.nodeArr[4].getChildByName("BackToMain").setActive(false);
    if (i) {
      if (l.lvid == n.lvid) {
        this.nodeArr[4].getChildByName("btn_playagain").setActive(true);
      } else {
        this.nodeArr[4].getChildByName("btn_next").setActive(true);
      }
    } else {
      this.nodeArr[4].getChildByName("btn_playagain").setActive(true);
    }
    r && this.nodeArr[4].setActive(false);
    var u = 1;
    if (i && r) {
      var p = this.game.curActivity;
      u = $2Manager.Manager.leveMgr.vo.curPassLv - p.unlockChapter;
      if (p.gameWinReward) {
        var f = $2GameUtil.GameUtil.deepCopy(p.gameWinReward);
        var h = $2ItemModel.default.instance.briefRewardArrTo(f);
        (e = this.rewardList).push.apply(e, h);
      }
    }
    var y = this.mode.getRewardlist(n.lvid);
    (t = this.rewardList).push.apply(t, y);
    this.rewardList.forEach(function (e) {
      e.num = Math.ceil(e.num * a * (1 + e.param * u));
    });
    this.nodeArr[3].getComponent(cc.ProgressBar).progress = a;
    this.nodeArr[3].getComByChild(cc.Label, "num").string = Math.floor(100 * a) + "%";
    this.isVideoReward = false;
    this.nodeArr[0].hideAllChildren();
    this.rewardList.forEach(function (e, t) {
      if (!(e.num <= 0)) {
        var i = o.nodeArr[0].children[t] || cc.instantiate(o.nodeArr[0].children[0]);
        i.setAttribute({
          parent: o.nodeArr[0],
          active: true
        });
        o.setItemInfo(e, i);
      }
    });
    this.labelArr[0].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
    this.labelArr[1].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
    if (!this.game) {
      this.nodeArr[4].setActive(false);
      this.nodeArr[3].setActive(false);
    }
    $2UIManager.UIManager.Close("ui/ModeChains/M33_FightBuffView");
    $2UIManager.UIManager.cleanQueue();
    $2Manager.Manager.ui.cleanDelayView();
  };
  _ctor.prototype.setItemInfo = function (e, t) {
    var o = e.type == $2GameSeting.GameSeting.GoodsType.Money ? $2Cfg.Cfg.CurrencyConfig.get(e.id) : $2Cfg.Cfg.RoleUnlock.get(e.id);
    $2Manager.Manager.loader.loadSpriteToSprit(o.icon, t.getComByChild(cc.Sprite, "icon"));
    $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(e.rarity).blockImg, t.getComByChild(cc.Sprite, "bg"));
    t.getComByChild(cc.Label, "val").string = e.num * (this.isVideoReward ? 2 : 1);
    cc.find("icon/fragments", t).setActive(e.type == $2GameSeting.GameSeting.GoodsType.Fragment);
    cc.tween(t).stopLast().set({
      opacity: 0
    }).delay(.1 * t.zIndex).to(.3, {
      opacity: 255
    }, {
      easing: cc.easing.backOut
    }).start();
  };
  _ctor.prototype.onClickDamagePanel = function (e) {
    var t = this;
    var o = e.target;
    if (1 == o.childrenCount) {
      o.children[0].setActive(!o.children[0].active);
    } else {
      if (99 == o.t_tempID) {
        return;
      }
      o.t_tempID = 99;
      $2Manager.Manager.loader.loadPrefab("ui/fight/DamagePanel", this.game.gameNode).then(function (e) {
        t.DamagePanel = e;
        e.setAttribute({
          parent: o,
          position: cc.v2(-e.children[0].width / 2 * 1.5, 500),
          scale: 1.5
        });
      });
    }
  };
  _ctor.prototype.onBtn = function (e, t) {
    var o = this;
    switch (t) {
      case "Replay":
        $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
          type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
          val: $2Manager.Manager.vo.switchVo.fightStamina,
          call: function (e) {
            if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Game_Replay);
              o.close();
            }
          }
        });
        break;
      case "NextLevel":
        this.nextCfg && $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
          type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
          val: $2Manager.Manager.vo.switchVo.fightStamina,
          call: function (e) {
            if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Game_NextLV, $2MVC.MVC.openArgs().setParam({
                id: o.nextCfg.lvid
              }));
              o.close();
            }
          }
        });
        break;
      case "BackToMain":
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
        this.close();
    }
  };
  _ctor.prototype.onVideoUnlock = function (e, t) {
    var o = +t;
    var i = $2Cfg.Cfg.MiniGameLv.get(o);
    var n = i.type;
    $2Manager.Manager.vo.knapsackVo.addGoods("ModeUnlockVideo_" + n + "_" + i.lvid);
    if ($2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + n + "_" + i.lvid) >= $2MoreGamesView.MoreGames.getUnlockNum(i.lvid)) {
      $2Manager.Manager.vo.userVo.unLockMode.push(n + "_" + i.lvid);
      $2Manager.Manager.vo.saveUserData();
      $2AlertManager.AlertManager.showNormalTips("解锁成功");
    }
    this.setInfo();
  };
  _ctor.prototype.onClickFrame = function () {
    var e;
    (null === (e = this.DamagePanel) || undefined === e ? undefined : e.active) && this.DamagePanel.setActive(false);
  };
  _ctor.prototype.onClose = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, this.rewardList, false);
    e.prototype.onClose.call(this);
  };
  cc__decorate([ccp_property([cc.Sprite])], _ctor.prototype, "imgArr", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/M33_Pop_GameEnd"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M33_Pop_GameEnd;