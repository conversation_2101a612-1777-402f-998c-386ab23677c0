const fs = require('fs');
const path = require('path');

// 分析文件结构的函数
function analyzeFileStructure(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // 查找编译后的类定义模式
        // 模式1: var exp_ClassName = function(e) { ... }
        const expClassMatches = content.match(/var\s+exp_(\w+)\s*=\s*function\s*\(/g) || [];

        // 模式2: exports.ClassName = undefined; 然后有对应的类定义
        const exportsMatches = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];

        // 模式3: 查找 ccclass 装饰器的痕迹
        const ccclassMatches = content.match(/@ccclass\s*(?:\([^)]*\))?\s*class\s+(\w+)/g) || [];

        // 模式4: 查找普通 class 定义
        const classMatches = content.match(/(?<!@ccclass\s*(?:\([^)]*\))?\s*)class\s+(\w+)/g) || [];

        // 模式5: 查找 enum 定义
        const enumMatches = content.match(/enum\s+(\w+)/g) || [];

        // 模式6: 查找 cc.Class 定义
        const ccClassMatches = content.match(/cc\.Class\s*\(\s*\{/g) || [];

        // 模式7: 查找 cc.Enum 定义
        const ccEnumMatches = content.match(/cc\.Enum\s*\(\s*\{/g) || [];

        // 模式8: 查找模块导出
        const moduleExports = content.match(/module\.exports\s*=/g) || [];

        // 模式9: 查找 __decorate 装饰器调用（编译后的装饰器）
        const decorateMatches = content.match(/__decorate\s*\(\s*\[\s*ccclass\s*(?:\([^)]*\))?\s*\]/g) || [];

        // 提取类名
        const expClassNames = expClassMatches.map(match => {
            const nameMatch = match.match(/var\s+exp_(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);

        const exportClassNames = exportsMatches.map(match => {
            const nameMatch = match.match(/exports\.(\w+)/);
            return nameMatch ? nameMatch[1] : '';
        }).filter(name => name);

        const result = {
            filePath: filePath,
            expClassCount: expClassMatches.length,
            exportsCount: exportsMatches.length,
            ccclassCount: ccclassMatches.length,
            classCount: classMatches.length,
            enumCount: enumMatches.length,
            ccClassCount: ccClassMatches.length,
            ccEnumCount: ccEnumMatches.length,
            moduleExportsCount: moduleExports.length,
            decorateCount: decorateMatches.length,
            expClassNames: expClassNames,
            exportClassNames: exportClassNames,
            ccclassMatches: ccclassMatches,
            classMatches: classMatches,
            enumMatches: enumMatches
        };

        // 判断是否为单类结构（编译后的文件）
        // 主要看 exp_ClassName 和 exports.ClassName 的数量
        const totalCompiledDefinitions = result.expClassCount + result.exportsCount;
        const totalOriginalDefinitions = result.ccclassCount + result.classCount + result.enumCount + result.ccClassCount + result.ccEnumCount;

        result.isSingleClass = (totalCompiledDefinitions === 1) || (totalOriginalDefinitions === 1);
        result.totalDefinitions = Math.max(totalCompiledDefinitions, totalOriginalDefinitions);

        // 判断是否可能是 ccclass（通过装饰器或命名模式）
        result.isPossibleCcclass = result.decorateCount > 0 || result.ccclassCount > 0;

        return result;
    } catch (error) {
        return {
            filePath: filePath,
            error: error.message,
            isSingleClass: false
        };
    }
}

// 获取 scripts_5 文件夹下的所有 JS 文件
function getJSFiles(dirPath) {
    const files = fs.readdirSync(dirPath);
    return files.filter(file => file.endsWith('.js')).map(file => path.join(dirPath, file));
}

// 主函数
function main() {
    const scriptsDir = './scripts_5';
    const jsFiles = getJSFiles(scriptsDir);
    
    console.log(`分析 ${jsFiles.length} 个 JS 文件...\n`);
    
    const singleCcclassFiles = [];
    const multiDefinitionFiles = [];
    const noDefinitionFiles = [];
    
    jsFiles.forEach(filePath => {
        const analysis = analyzeFileStructure(filePath);
        
        if (analysis.error) {
            console.log(`错误: ${analysis.filePath} - ${analysis.error}`);
            return;
        }

        const fileName = path.basename(filePath);

        if (analysis.isSingleClass) {
            singleCcclassFiles.push(analysis);
            const className = analysis.expClassNames[0] || analysis.exportClassNames[0] || '未知';
            const typeInfo = analysis.isPossibleCcclass ? 'ccclass' : 'class';
            console.log(`✓ 单${typeInfo}: ${fileName} - ${className}`);
        } else if (analysis.totalDefinitions > 1) {
            multiDefinitionFiles.push(analysis);
            console.log(`✗ 多定义: ${fileName} - exp:${analysis.expClassCount}, exports:${analysis.exportsCount}, ccclass:${analysis.ccclassCount}, class:${analysis.classCount}, enum:${analysis.enumCount}`);
        } else if (analysis.totalDefinitions === 0) {
            noDefinitionFiles.push(analysis);
            console.log(`? 无定义: ${fileName}`);
        } else {
            console.log(`- 其他: ${fileName} - exp:${analysis.expClassCount}, exports:${analysis.exportsCount}, ccclass:${analysis.ccclassCount}, class:${analysis.classCount}, enum:${analysis.enumCount}`);
        }
    });
    
    console.log(`\n=== 分析结果 ===`);
    console.log(`单类文件: ${singleCcclassFiles.length} 个`);
    console.log(`多定义文件: ${multiDefinitionFiles.length} 个`);
    console.log(`无定义文件: ${noDefinitionFiles.length} 个`);

    // 输出单类文件列表
    if (singleCcclassFiles.length > 0) {
        console.log(`\n=== 单类文件列表 ===`);
        singleCcclassFiles.forEach(file => {
            const className = file.expClassNames[0] || file.exportClassNames[0] || '未知';
            const typeInfo = file.isPossibleCcclass ? '[ccclass]' : '[class]';
            console.log(`${path.basename(file.filePath)} - ${className} ${typeInfo}`);
        });
    }

    // 保存结果到 JSON 文件
    const result = {
        singleClassFiles: singleCcclassFiles.map(f => ({
            fileName: path.basename(f.filePath),
            filePath: f.filePath,
            className: f.expClassNames[0] || f.exportClassNames[0] || '未知',
            isPossibleCcclass: f.isPossibleCcclass,
            expClassCount: f.expClassCount,
            exportsCount: f.exportsCount,
            ccclassCount: f.ccclassCount
        })),
        multiDefinitionFiles: multiDefinitionFiles.map(f => ({
            fileName: path.basename(f.filePath),
            filePath: f.filePath,
            expClassCount: f.expClassCount,
            exportsCount: f.exportsCount,
            ccclassCount: f.ccclassCount,
            classCount: f.classCount,
            enumCount: f.enumCount,
            ccClassCount: f.ccClassCount,
            ccEnumCount: f.ccEnumCount,
            totalDefinitions: f.totalDefinitions
        })),
        noDefinitionFiles: noDefinitionFiles.map(f => ({
            fileName: path.basename(f.filePath),
            filePath: f.filePath
        }))
    };
    
    fs.writeFileSync('ccclass_analysis_result.json', JSON.stringify(result, null, 2));
    console.log(`\n结果已保存到 ccclass_analysis_result.json`);
}

main();
