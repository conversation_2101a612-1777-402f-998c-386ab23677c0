var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_Bullet_RandomMove = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._deltaTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this._deltaTime = 0;
  };
  _ctor.prototype.changeAngle = function () {
    var e = this.game.scenceSize;
    var t = $2GameUtil.GameUtil.GetAngle(this.node.position, cc.v2($2Game.Game.random(e[0], e[1]), $2Game.Game.random(this.node.y > .8 * e[3] ? e[2] : e[3], e[3]))) + 90;
    var o = $2GameUtil.GameUtil.AngleAndLenToPos(t).clone();
    $2Game.Game.tween(this.vo.shootDir).stopLast().to(.3, {
      x: o.x,
      y: o.y
    }).start();
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (this.isActive && (this._deltaTime += t) > 1) {
      this.changeAngle();
      this._deltaTime = 0;
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_RandomMove")], _ctor);
}($2Bullet.default);
exports.default = def_Bullet_RandomMove;