var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();
var def_Bullet_HitReflex = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.reflexNum = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this.reflexNum = 0;
  };
  _ctor.prototype.onCollisionEnter = function (t, o) {
    if (e.prototype.onCollisionEnter.call(this, t, o)) {
      if (this.reflexNum >= this.vo.skillCfg.reflexCount) {
        return void (this.vo.lifeTime = 0);
      }
      cc.Vec2.subtract(u, this.position, t.position);
      u.normalizeSelf();
      this.vo.shootDir.set(u);
      this.reflexNum++;
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_HitReflex")], _ctor);
}($2Bullet.default);
exports.default = def_Bullet_HitReflex;