var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var f = cc.v2();
var def_TrackBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._scearchTime = 1;
    t._deltaTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (e) {
    this._vo = e;
    this._deltaTime = 1;
  };
  _ctor.prototype.nextTarget = function () {
    var e = this.vo.belongSkill.getOwner();
    var t = this.vo.belongSkill.cutVo.dis;
    1540 == this.vo.belongSkill.skillMainID && (t += 600);
    var o = $2Game.Game.mgr.LatticeElementMap.seekByPos({
      pos: this.node.position,
      radius: t,
      targetCamp: [e.atkCamp]
    });
    if (o.length > 0) {
      this._target = $2GameUtil.GameUtil.getRandomInArray(o)[0];
      this._vo.shootDir = this._target.position.sub(this.node.position).normalize();
    }
  };
  _ctor.prototype.onUpdate = function (t) {
    if ((this._deltaTime += t) >= this._scearchTime) {
      this._deltaTime = 0;
      this.nextTarget();
    }
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.onCollisionStay = function (e) {
    var t = e.comp;
    if (t && this.vo.atkCamp.includes(e.comp.campType)) {
      t.behit(this.vo.hurt);
      this.vo.shootDir.mul(this.vo.belongSkill.cutVo.repelDic / 100, f);
      cc.Vec2.add(f, f, t.position);
      t.setPosition(f);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/TrackBullet")], _ctor);
}($2Bullet.default);
exports.default = def_TrackBullet;