var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Game = require("Game");
var $2GameEffect = require("GameEffect");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_Effect_Behead = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    var t = this;
    e.prototype.init.call(this);
    this.deadTime = 1;
    $2Game.Game.tween(this.node).set({
      scale: 0,
      opacity: 255
    }).to(.05, {
      scale: $2Game.Game.random(15, 20) / 10
    }).by(.5, {
      y: 100
    }).to(.1, {
      scale: 0,
      opacity: 0
    }).call(function () {
      t.setDead();
    }).start();
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2GameEffect.default);
exports.default = def_Effect_Behead;