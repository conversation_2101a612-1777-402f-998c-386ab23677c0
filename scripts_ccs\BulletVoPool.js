var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BulletVoPool = undefined;
var $1$2Pool = require("Pool");
var r = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.Spawner<PERSON> = "BulletVo";
    return t;
  }
  cc__extends(t, e);
  return t;
}($1$2Pool.PoolMgrBase);
exports.BulletVoPool = new r("BulletVoPool");