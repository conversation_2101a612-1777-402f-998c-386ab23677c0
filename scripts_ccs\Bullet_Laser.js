var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_Bullet_Laser = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._hurtTime = 0;
    t._hurtNum = 1;
    t._dtTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.set = function (e) {
    for (var t in e) {
      this[t] = e[t];
    }
    this._dtTime = .1;
    this._hurtTime = 0;
    this._hurtNum = 1;
  };
  _ctor.prototype.checkTargetIsDead = function (e) {
    return !e.isValid || e.isDead;
  };
  _ctor.prototype.setBullet = function () {
    return !!this.endTarget && (this.checkTargetIsDead(this.firstTarget) || this.checkTargetIsDead(this.endTarget) || this.checkTargetIsDead(this.startTarget) ? (this.checkTargetIsDead(this.firstTarget) && this.vo.belongSkill.excuteEnd(), this.vo.lifeTime = 0, this.vo.isForever = false, this.node.height = 0, this.collider.size.height = this.node.height, this.collider.offset.y = this.node.height / 2, this.endTarget = null, false) : (this.node.setAttribute({
      opacity: 255,
      height: cc.Vec2.distance(this.startTarget.bodyPosition, this.endTarget.bodyPosition) / this.vo.scale,
      angle: $2GameUtil.GameUtil.GetAngle(this.startTarget.bodyPosition, this.endTarget.bodyPosition) + 90,
      position: this.startTarget.bodyPosition
    }), this.collider.size.height = this.node.height, this.collider.offset.y = this.node.height / 2, true));
  };
  _ctor.prototype.atkNextTarget = function () {
    var e = this;
    if (0 != this.canAnsNum) {
      var t = this.endTarget;
      var o = $2Game.Game.mgr.LatticeElementMap.seekByPos({
        pos: t.bodyPosition,
        radius: this.vo.belongSkill.dis,
        targetCamp: [$2BaseEntity.CampType.Two]
      });
      o.sort(function (e, t) {
        return t.curHp - e.curHp;
      });
      var i = o.splice(0, 1)[0];
      if (i) {
        var n = this.vo.belongSkill.getBulletVo({
          lifeTime: this.vo.belongSkill.cutVo.dur
        });
        $2Game.Game.mgr.spawnBullet(this.vo.bulletPath, n, {
          opacity: 0
        }).then(function (o) {
          o.set({
            startTarget: t,
            endTarget: i,
            firstTarget: e.firstTarget,
            canAnsNum: e.canAnsNum - 1
          });
        });
      }
    }
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (this.setBullet() && (this._dtTime -= t) < 0) {
      this._dtTime = this.vo.belongSkill.cutVo.dur;
      this.atkNextTarget();
    }
  };
  _ctor.prototype.onCollisionStay = function (e) {
    var t = e.comp;
    t && this.vo.atkCamp.includes(e.comp.campType) && t.behit(this.vo.hurt) && this._hurtNum++;
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_Laser")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_Laser;