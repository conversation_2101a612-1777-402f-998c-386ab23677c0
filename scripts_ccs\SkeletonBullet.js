var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_SkeletonBullet = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    var t = this;
    e.prototype.onLoad.call(this);
    this.mySkeleton = this.node.getComByChild(sp.Skeleton) || this.node.getComponent(sp.Skeleton);
    this.mySkeleton.setCompleteListener(function () {
      t.setDead();
    });
  };
  _ctor.prototype.onEnable = function () {
    this.mySkeleton.setAnimation(0, this.mySkeleton.defaultAnimation, this.mySkeleton.loop);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/SkeletonBullet")], _ctor);
}($2Bullet.default);
exports.default = def_SkeletonBullet;