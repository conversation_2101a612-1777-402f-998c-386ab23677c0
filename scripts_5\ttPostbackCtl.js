Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2SdkConfig = require("SdkConfig");
var $2index = require("index");
var $2Report = require("Report");
var def_ttPostbackCtl = function () {
  function _ctor() {
    this._sdk = $2index.default;
  }
  _ctor.GetInstance = function () {
    _ctor.instance || (_ctor.instance = new _ctor());
    return this.instance;
  };
  _ctor.prototype.init = function () {
    var e;
    if ($2index.minSdk) {
      (e = {})[$2SdkConfig.EPlatform.KWAI_MICRO] = "ks_minigame";
      e[$2SdkConfig.EPlatform.BYTE_DANCE] = "tt_minigame";
      e[$2SdkConfig.EPlatform.WECHAT_GAME] = "weixin";
      var t = e;
      var o = wonderSdk.BMS_APP_NAME;
      var r = wonderSdk.BMS_VERSION;
      this._sdk.initParams({
        app_name: o,
        channel: t[wonderSdk.platformId],
        version: r,
        log_level: "info"
      });
    }
  };
  _ctor.prototype.adRequest = function (e) {
    $2Report.default.adRequest(e);
  };
  _ctor.prototype.adClick = function (e) {
    $2Report.default.adClick(e);
  };
  _ctor.prototype.adImpression = function (e) {
    $2Report.default.adImpression(e);
  };
  _ctor.prototype.adFill = function (e) {
    $2Report.default.adFill(e);
  };
  _ctor.prototype.adImpressionDone = function (e) {
    $2Report.default.adImpressionDone(e);
  };
  _ctor.instance = null;
  return _ctor;
}();
exports.default = def_ttPostbackCtl;