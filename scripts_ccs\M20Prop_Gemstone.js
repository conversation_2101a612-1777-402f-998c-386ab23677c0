var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2GameSeting = require("GameSeting");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2PropertyVo = require("PropertyVo");
var $2MBackpackHero = require("MBackpackHero");
var $2M20Prop = require("M20Prop");
var $2M20Prop_Equip = require("M20Prop_Equip");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_M20Prop_Gemstone = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  Object.defineProperty(_ctor.prototype, "roleID", {
    get: function () {
      var e;
      return (null === (e = this.gemData) || undefined === e ? undefined : e.MagicallyChangeID) || this.roleCfg.id;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "gemCfg", {
    get: function () {
      if (this.gemData.MagicallyChangeID) {
        return $2Cfg.Cfg.EquipMergeLv.get(this.roleID);
      } else {
        return this.mergeCfg;
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.excute = function () {
    if (this.roleCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange) {
      this.setReaction({
        type: $2MBackpackHero.MBPack.ReactionType.MagicallyChange,
        item: this,
        prem: $2GameUtil.GameUtil.getRandomByWeightInList(this.mergeCfg.gemWeight)[0].id
      });
    } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GeGeJi) {
      this.gemData.eggOpenNum = 1, this.showTips("<outline color=black width=2>点击下蛋</outline>", true);
    }
  };
  _ctor.prototype.onTouchEnd = function (t) {
    e.prototype.onTouchEnd.call(this, t);
    if (this._frameNum < 6 && this.isUnlock) {
      if (this.gemData.eggOpenNum > 0) {
        this.setReaction({
          type: $2MBackpackHero.MBPack.ReactionType.GeGeJi,
          item: this
        });
      } else {
        $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_GemInfo", $2MVC.MVC.openArgs().setParam(this));
      }
    }
  };
  _ctor.prototype.set = function (t, o) {
    var i = this;
    e.prototype.set.call(this, t, o);
    this.resetState(cc.rect($2MBackpackHero.MBPack.BlockSize, $2MBackpackHero.MBPack.BlockSize));
    this.gemData = {
      id: this.roleID,
      mergeID: t.id,
      MagicallyChangeID: 0,
      GreedyGemstoneNum: 0,
      SacrificeNum: 5,
      eggOpenNum: 0
    };
    this.setProperty();
    this.initHp();
    this.node.getChildByName("gemStones").setActive(false);
    this.lineBox = this.node.getORaddChildByName("line");
    this.resetGemState();
    this.scheduleOnce(function () {
      i.showTips();
    }, .5);
  };
  _ctor.prototype.resetGemState = function () {
    this.node.getComByChild(cc.Label, "name").setAttribute({
      string: this.gemCfg.name
    }).node.setActive(true);
    this.node.getComByChild(cc.Label, "num").setAttribute({
      string: this.gemData.SacrificeNum
    }).node.setActive(this.roleID == $2MBackpackHero.MBPack.ReactionType.Sacrifice);
  };
  Object.defineProperty(_ctor.prototype, "lineTarget", {
    get: function () {
      var e = this;
      if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone) {
        return cc__spreadArrays(this.packView.allList, [this.packView.selectProp]).filter(function (t) {
          return t instanceof $2M20Prop_Equip.default && !t.gemStones.find(function (e) {
            return e.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone;
          }) && e.gemCfg.relateEquip.includes(t.mergeCfg.id);
        });
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setProperty = function () {
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
  };
  _ctor.prototype.checkReaction = function (e, t) {
    var i;
    undefined === t && (t = false);
    if (e && this != e && e.isUnlock) {
      if (e instanceof $2M20Prop_Equip.default) {
        if (this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyEquip) {
          return {
            type: this.roleID,
            item: e,
            d: 0
          };
        }
        if (this.roleID == $2MBackpackHero.MBPack.ReactionType.MinEquip) {
          if (e.blockType <= 1) {
            t && $2AlertManager.AlertManager.showNormalTips("1格装备无法缩小");
          } else {
            if (this.gemCfg.relateEquip.includes(e.mergeCfg.id)) {
              return {
                type: this.roleID,
                item: e,
                d: 0
              };
            }
            t && $2AlertManager.AlertManager.showNormalTips("只能缩小4级进阶装备");
          }
        } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.DestinyGem) {
          if (this.gemCfg.relateEquip.includes(e.mergeCfg.id)) {
            return {
              type: this.roleID,
              item: e,
              d: 0
            };
          }
          t && $2AlertManager.AlertManager.showNormalTips("只能升级装备");
        } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.MOONCAKE) {
          if (3 != e.mergeCfg.lv || e.property.cut.atk <= 0) {
            t && $2AlertManager.AlertManager.showNormalTips("只能镶嵌3级武器");
          } else {
            if (![5020].includes(e.mergeCfg.id)) {
              return {
                type: this.roleID,
                item: e,
                d: 0
              };
            }
            t && $2AlertManager.AlertManager.showNormalTips("无法复制");
          }
        } else if (null === (i = this.gemCfg.relateEquip) || undefined === i ? undefined : i.includes(e.mergeCfg.id)) {
          return {
            type: $2MBackpackHero.MBPack.ReactionType.Mosaic,
            item: e,
            d: 0
          };
        }
        if (t) {
          this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyGem && $2AlertManager.AlertManager.showNormalTips("只能克隆其他宝石");
          this.roleID == $2MBackpackHero.MBPack.ReactionType.Sacrifice && $2AlertManager.AlertManager.showNormalTips("无法镶嵌,点击宝石看详情");
        }
      } else if (e instanceof o && this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyGem) {
        return {
          type: $2MBackpackHero.MBPack.ReactionType.CopyGem,
          item: e,
          d: 0
        };
      }
      return null;
    }
  };
  _ctor.prototype.setReaction = function (e) {
    var t;
    var o = this;
    var i = e.item;
    switch (e.type) {
      case $2MBackpackHero.MBPack.ReactionType.Mosaic:
        if (i instanceof $2M20Prop_Equip.default) {
          if (i.isGemMax) {
            this.packView.setInSpareBox(this);
            return $2AlertManager.AlertManager.showNormalTips("镶嵌满了");
          }
          if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
            if ($2MBackpackHero.MBPack.equipWeapon.includes(this.gemCfg.relateEquip[0])) {
              this.packView.setInSpareBox(this);
              return $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("只能镶嵌在%s上", "武器"));
            }
            if ($2MBackpackHero.MBPack.equipArmor.includes(this.gemCfg.relateEquip[0])) {
              this.packView.setInSpareBox(this);
              return $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("只能镶嵌在%s上", "防具"));
            }
          }
          if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone) {
            if (i.gemStones.find(function (e) {
              return e.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone;
            })) {
              this.packView.setInSpareBox(this);
              return $2AlertManager.AlertManager.showNormalTips("当前装备已镶嵌贪婪宝石");
            }
            if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
              this.packView.setInSpareBox(this);
              return $2AlertManager.AlertManager.showNormalTips("贪婪宝石只能镶嵌在4级进阶装备");
            }
            i.setMosaic(this);
            $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_Turntable", $2MVC.MVC.openArgs().setParam(i));
          } else if (this.mergeCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange) {
            this.packView.newProp(this.gemCfg, {
              position: i.position
            }).then(function (e) {
              i.setMosaic(e);
            });
            this.unuse();
          } else {
            i.setMosaic(this);
          }
          this.showMosaicEffect(i);
          this.changeToch(false);
          this.node.setActive(false);
        }
        break;
      case $2MBackpackHero.MBPack.ReactionType.DestinyGem:
        if (i.isMaxLv) {
          this.packView.setInSpareBox(this);
          return $2AlertManager.AlertManager.showNormalTips("该装备已满级");
        }
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
        i.node.setActive(false);
        t = this.getWeight(i);
        $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs5_activation", {
          nodeAttr: {
            parent: this.packView.topEffectNode,
            position: this.position
          },
          spAttr: {
            animation: "animation" + (t ? 2 : 1),
            type: $2GameSeting.GameSeting.TweenType.Not,
            loop: false
          },
          delayRemove: 1
        }, this.game.gameNode);
        this.packView.scheduleOnce(function () {
          if (t) {
            i.node.setActive(true);
            i.upgrade();
            !i.isMaxLv && o.packView.scheduleOnce(function () {
              i.upgrade();
            }, .3);
          } else {
            $2AlertManager.AlertManager.showNormalTips("提升失败");
            i.unuse();
          }
        }, .8);
        this.unuse();
        break;
      case $2MBackpackHero.MBPack.ReactionType.CopyEquip:
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
        t = this.getWeight(i);
        $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs3_activation", {
          delayRemove: 1,
          nodeAttr: {
            parent: this.packView.topEffectNode,
            position: this.position
          },
          spAttr: {
            animation: "animation" + (t ? 2 : 1),
            type: $2GameSeting.GameSeting.TweenType.Not,
            loop: false
          }
        }, this.game.gameNode);
        this.packView.scheduleOnce(function () {
          if (t) {
            o.packView.newProp(i.mergeCfg, {
              position: i.position
            }).then(function (e) {
              var t;
              o.packView.setInSpareBox(e);
              i.isUnlock || e.setVideoLock();
              null === (t = e.buffMgr) || undefined === t || t.add(5e4);
              $2AlertManager.AlertManager.showNormalTips("复制成功");
            });
          } else {
            $2AlertManager.AlertManager.showNormalTips("复制失败");
          }
        }, 1);
        this.unuse();
        break;
      case $2MBackpackHero.MBPack.ReactionType.CopyGem:
        if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
          this.packView.setInSpareBox(this);
          return $2AlertManager.AlertManager.showNormalTips("无法克隆该宝石");
        }
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
        t = this.getWeight(i);
        $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs2_activation", {
          delayRemove: 1,
          nodeAttr: {
            parent: this.packView.topEffectNode,
            position: this.position
          },
          spAttr: {
            animation: "animation" + (t ? 2 : 1),
            type: $2GameSeting.GameSeting.TweenType.Not,
            loop: false
          }
        }, this.game.gameNode);
        this.packView.scheduleOnce(function () {
          t && o.packView.newProp(i.gemCfg, {
            position: i.position
          }).then(function (e) {
            o.packView.setInSpareBox(e);
            e.setUnlock();
          });
          $2AlertManager.AlertManager.showNormalTips(t ? "克隆成功" : "克隆失败");
        }, 1);
        this.unuse();
        break;
      case $2MBackpackHero.MBPack.ReactionType.MOONCAKE:
        if (i instanceof $2M20Prop_Equip.default) {
          t = this.getWeight(i);
          $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs20_activation", {
            delayRemove: 1,
            nodeAttr: {
              parent: this.packView.topEffectNode,
              position: this.position
            },
            spAttr: {
              animation: "animation" + (t ? 2 : 1),
              type: $2GameSeting.GameSeting.TweenType.Not,
              loop: false
            }
          }, this.game.gameNode);
          this.packView.scheduleOnce(function () {
            if (t) {
              o.packView.newProp($2Cfg.Cfg.EquipMergeLv.get(5020), {
                position: i.position
              }).then(function (e) {
                o.packView.setInSpareBox(e);
                i.skillMgr.clearAll();
                e.cloneBy(i);
                $2AlertManager.AlertManager.showNormalTips("成功");
                i.unuse();
              });
            } else {
              $2AlertManager.AlertManager.showNormalTips("失败");
            }
            o.unuse();
          }, 1);
        }
        break;
      case $2MBackpackHero.MBPack.ReactionType.Sacrifice:
        if (i instanceof $2M20Prop_Equip.default) {
          var n = this.gemCfg.gemWeight.filter(function (e) {
            return e[2] == i.mergeCfg.lv;
          });
          if (n.length > 0) {
            i.unuse();
            this.gemData.SacrificeNum--;
            var r = $2Cfg.Cfg.EquipMergeLv.get($2GameUtil.GameUtil.getRandomByWeightInList(n)[0].id);
            $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
            $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs1_activation", {
              nodeAttr: {
                parent: this.packView.topEffectNode,
                position: this.position
              },
              spAttr: {
                animation: "animation",
                type: $2GameSeting.GameSeting.TweenType.Not,
                loop: false
              },
              delayRemove: 1
            }, this.game.gameNode);
            this.scheduleOnce(function () {
              o.packView.newProp(r, {
                position: o.position
              }).then(function (e) {
                e.setUnlock();
                cc.tween(e.node).to(.2, {
                  scale: 1.3,
                  position: cc.Vec2.ZERO
                }).by(.4, {
                  angle: 30
                }).by(.4, {
                  angle: -30
                }).call(function () {
                  var t;
                  null === (t = o.packView) || undefined === t || t.setInSpareBox(e);
                }).start();
              });
              o.gemData.SacrificeNum <= 0 && o.unuse();
            }, 1);
          } else {
            $2AlertManager.AlertManager.showNormalTips("只能献祭合成等级为2,3,4的装备");
            this.packView.setInSpareBox(i);
          }
        }
        break;
      case $2MBackpackHero.MBPack.ReactionType.MagicallyChange:
        if (0 == e.prem) {
          return;
        }
        this.gemData.MagicallyChangeID = e.prem;
        var a = this.gemCfg;
        $2Manager.Manager.loader.loadSpriteToSprit(a.res, this.img, this.packView.node);
        this.gemData.SacrificeNum = 5;
        this.img.node.setAttribute({
          active: true,
          opacity: 0
        });
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
        $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs4_activation", {
          nodeAttr: {
            parent: this.packView.topEffectNode,
            position: this.position
          },
          spAttr: {
            animation: "animation",
            type: $2GameSeting.GameSeting.TweenType.Not,
            loop: false
          }
        }, this.game.gameNode).then(function (e) {
          o.mySkeleton.node.setActive(false);
          cc.tween(e.node).delay(.5).call(function () {
            cc.tween(o.img.node).stopLast().set({
              active: true
            }).to(.3, {
              opacity: 255
            }).to(.5, {
              opacity: 100
            }).union().repeatForever().start();
            e.destroy();
          }).start();
        });
        break;
      case $2MBackpackHero.MBPack.ReactionType.MinEquip:
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
        t = this.getWeight(i);
        if (i instanceof $2M20Prop_Equip.default) {
          $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs11_activation", {
            delayRemove: 1,
            nodeAttr: {
              parent: this.packView.topEffectNode,
              position: this.position
            },
            spAttr: {
              animation: "animation" + (t ? 2 : 1),
              type: $2GameSeting.GameSeting.TweenType.Not,
              loop: false
            }
          }, this.game.gameNode);
          this.packView.scheduleOnce(function () {
            t && i.setMin();
            $2AlertManager.AlertManager.showNormalTips(t ? "缩小成功" : "缩小失败");
          }, 1);
          this.unuse();
        }
        break;
      case $2MBackpackHero.MBPack.ReactionType.GeGeJi:
        this.gemData.eggOpenNum--;
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_GeGeJi");
    }
    this.resetGemState();
    this.packView.scheduleOnce(function () {
      o.game.saveRecordVo();
    }, 2);
  };
  _ctor.prototype.getWeight = function (e) {
    var t;
    var o;
    var i;
    var n = this;
    if (this.gemCfg.equipId == this.roleCfg.id) {
      (o = this.game.rVo.gemWeightList)[i = this.roleID] || (o[i] = 0);
      var r = null === (t = $2Manager.Manager.vo.switchVo.gemSuccessWeight.find(function (e) {
        return e[0] == n.game.level;
      }) || $2Manager.Manager.vo.switchVo.gemSuccessWeight.lastVal) || undefined === t ? undefined : t[1 + this.game.rVo.gemWeightList[this.roleID]];
      this.game.rVo.gemWeightList[this.roleID]++;
      if (r) {
        return $2GameUtil.GameUtil.weightFloat(r);
      }
    }
    if ([$2MBackpackHero.MBPack.ReactionType.CopyEquip, $2MBackpackHero.MBPack.ReactionType.MinEquip].includes(this.roleID)) {
      return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[e.mergeCfg.lv - 1]);
    } else {
      if ([$2MBackpackHero.MBPack.ReactionType.CopyGem].includes(this.roleID)) {
        return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[e.roleCfg.rarity - 1]);
      } else {
        return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[0]);
      }
    }
  };
  Object.defineProperty(_ctor.prototype, "saveData", {
    get: function () {
      return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock,
        gemData: this.gemData
      };
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.readData = function (t) {
    e.prototype.readData.call(this, t);
    this.gemData = t.gemData;
    if (this.roleCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange && this.isUnlock) {
      this.setReaction({
        type: $2MBackpackHero.MBPack.ReactionType.MagicallyChange,
        item: this,
        prem: this.gemData.MagicallyChangeID
      });
    } else {
      this.resetGemState();
    }
  };
  _ctor.prototype.showMosaicEffect = function (e) {
    $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.ui_addgem);
    $2Manager.Manager.loader.loadSpineNode("bones/gem/fx_bsset", {
      nodeAttr: {
        parent: e.node,
        position: cc.Vec2.ZERO
      },
      spAttr: {
        animation: "animation",
        type: $2GameSeting.GameSeting.TweenType.Not,
        loop: false
      },
      delayRemove: 1
    }, this.game.gameNode);
  };
  _ctor.prototype.update = function () {
    var e = this;
    if (this.isValid && this.game) {
      this.lineBox.hideAllChildren();
      [$2MBackpackHero.MBPack.ReactionType.GreedyGemstone].includes(this.roleID) && this.lineTarget.forEach(function (t, o) {
        var i = $2GameUtil.GameUtil.GetAngle(e.node.position, t.node.position) + 90;
        var n = cc.Vec2.distance(e.node.position, t.node.position) / 393;
        (e.lineBox.children[o] || cc.instantiate(e.packView.linePrefab).setAttribute({
          parent: e.lineBox
        })).setAttribute({
          angle: i,
          scale: n,
          active: true
        });
      });
    }
  };
  return o = cc__decorate([ccp_ccclass], _ctor);
}($2M20Prop.default);
exports.default = def_M20Prop_Gemstone;