var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2GameUtil = require("GameUtil");
var $2M20_ShopPartItem = require("M20_ShopPartItem");
var $2M20_Shop_HeroItem = require("M20_Shop_HeroItem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_M20_ShopPartItem_hero = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.resetView = function () {
    e.prototype.resetView.call(this);
    for (var t = 0; t < this.content.length; t++) {
      var o = this.content[t];
      var i = this.contentnode.children[t] || cc.instantiate(this.cloneitem);
      i.setAttribute({
        parent: this.contentnode
      });
      i.getComponent($2M20_Shop_HeroItem.default).setdata(o);
    }
  };
  _ctor.prototype.getList = function () {
    return cc__spreadArrays($2GameUtil.GameUtil.getRandomInArray($2Cfg.Cfg.BagShopItem.filter({
      type: 999
    }), 1));
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2M20_ShopPartItem.default);
exports.default = def_M20_ShopPartItem_hero;