var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BulletBase = require("BulletBase");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_WallBase = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._entityType = $2BaseEntity.EntityType.Neutrality;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onCollisionEnter = function (e) {
    if (e.comp instanceof $2BulletBase.default) {
      if (e.comp.vo.skillCfg.dur) {
        return;
      }
      e.comp.isDead = true;
    }
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2OrganismBase.default);
exports.default = def_WallBase;