var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2StateMachine = require("StateMachine");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Buff = require("Buff");
var $2SkillManager = require("SkillManager");
var $2PetState = require("PetState");
var $2PropertyVo = require("PropertyVo");
var $2BaseEntity = require("BaseEntity");
var $2SteeringBehaviors = require("SteeringBehaviors");
var $2Vehicle = require("Vehicle");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc.v2();
cc.v2();
var def_Pet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.followGap = 400;
    t._stateMachine = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "targetGap", {
    get: function () {
      var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this._parent) || undefined === e ? undefined : e.position);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
      this.mySkeleton.clearTracks();
      if (this.mySkeleton) {
        null === (t = this.mySkeleton) || undefined === t || t.clearTracks();
        $2Manager.Manager.loader.loadSpine(e.spine, this.game.gameNode).then(function (e) {
          o.mySkeleton.reset(e);
          o.playAction("idle", true);
          o.delayByGame(function () {
            o.onNewSize(o.roleNode.getContentSize());
          });
        });
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.multiplySelf(this.colliderScaleSet.w, this.colliderScaleSet.h);
    this.node.setContentSize(t.width, t.height);
    this.collider.setSize(t);
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this._steering || (this._steering = new $2SteeringBehaviors.default(this));
    this.property = new $2PropertyVo.Property.Vo(this);
    this.entityType = $2BaseEntity.EntityType.Pet;
    this.campType = $2BaseEntity.CampType.One;
  };
  _ctor.prototype.setPet = function (e, t) {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
    var o = $2Cfg.Cfg.Role.filter({
      roleId: e,
      lv: 1
    })[0];
    this.property.set(o);
    this.updateProperty();
    this.parent = t;
    this.campType = t.campType;
    this.myData.startSkill && this.addSkill(this.myData.startSkill, true, true);
    this.myData.startBuff && this.addBuff(this.myData.startBuff);
    this.registerState();
    this.initHp();
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.game.showDamageDisplay(e, this.position);
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.curHp <= 0 && this.toDead(e);
      wonderSdk.vibrate(0);
      return e;
    }
  };
  _ctor.prototype.registerState = function () {
    if (!this._stateMachine) {
      this._stateMachine = new $2StateMachine.State.Machine(this);
      this._stateMachine.addState(new $2PetState.PetState.IdleState(this));
      this._stateMachine.addState(new $2PetState.PetState.DeadState(this));
      this._stateMachine.addState(new $2PetState.PetState.WanderState(this));
      this._stateMachine.addState(new $2PetState.PetState.FollowState(this));
      this._stateMachine.registerGlobalState(new $2PetState.PetState.AttackState(this));
    }
  };
  _ctor.prototype.toIdle = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.IDLE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
  };
  _ctor.prototype.toWander = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.WANDER) || this._stateMachine.changeState($2StateMachine.State.Type.WANDER);
  };
  _ctor.prototype.toFollow = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.FOLLOW) || this._stateMachine.changeState($2StateMachine.State.Type.FOLLOW);
  };
  _ctor.prototype.toDead = function () {
    this._stateMachine.isHasState([$2StateMachine.State.Type.DEAD]) || this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2Vehicle.default);
exports.default = def_Pet;