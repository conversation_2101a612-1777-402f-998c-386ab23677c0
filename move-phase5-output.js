const fs = require('fs');
const path = require('path');

/**
 * 将第五阶段脚本自动转换的TypeScript文件从output目录移动到output_5目录
 * 这样可以清楚区分哪些是脚本自动转换的，哪些是手动转换的
 */
class Phase5OutputMover {
    constructor() {
        this.phase5Files = [
            'ADController.ts',
            'ADModel.ts',
            'AlertManager.ts',
            'Api.ts',
            'ArcBullet.ts',
            'AssetLoader.ts',
            'AutoAmTool.ts',
            'AutoAnimationClip.ts',
            'AutoFollow.ts',
            'AutoScaleComponent.ts',
            'BackHeroProp.ts',
            'BackpackHeroHome.ts',
            'BagModeSkillPoolCfg.ts',
            'bagMonsterLvCfg.ts',
            'BagShopItemCfg.ts',
            'BagSkillCfg.ts',
            'BoomerangBullet.ts',
            'BottomBarController.ts',
            'BottomBarModel.ts',
            'BottomBarView.ts',
            'BounceBullet.ts',
            'BoxLevelExpCfg.ts',
            'BronMonsterManger.ts',
            'Buff.ts',
            'BuffCardItem.ts',
            'BuffCfg.ts',
            'BuffController.ts',
            'BuffList.ts',
            'BuffModel.ts',
            'BuildModeSkiilpoolCfg.ts',
            'Bullet.ts',
            'BulletBase.ts',
            'BulletEffectCfg.ts',
            'BulletVoPool.ts',
            'Bullet_Arrow.ts',
            'Bullet_FollowTarget.ts',
            'Bullet_HitReflex.ts',
            'Bullet_Laser.ts',
            'Bullet_Ligature.ts',
            'Bullet_LigaturePonit.ts',
            'Bullet_Path.ts',
            'Bullet_RandomMove.ts',
            'Bullet_RigidBody.ts',
            'ByteDance.ts',
            'Cfg.ts',
            'CircleBullet.ts',
            'Commonguide.ts',
            'CompManager.ts',
            'config.ts',
            'ContinuousBullet.ts',
            'CurrencyConfigCfg.ts',
            'DialogBox.ts',
            'dmmItemCfg.ts',
            'dmmRoleCfg.ts',
            'Dragon.ts',
            'DragonBody.ts',
            'dragonPathCfg.ts',
            'DropConfigCfg.ts',
            'EaseScaleTransition.ts',
            'EffectSkeleton.ts',
            'Effect_Behead.ts',
            'Effect_Behit.ts',
            'EnergyStamp.ts',
            'EntityDieEffect.ts',
            'EquipLvCfg.ts',
            'EquipMergeLvCfg.ts',
            'EventController.ts',
            'EventModel.ts',
            'ExchangeCodeView.ts',
            'FBoxCollider.ts',
            'FCircleCollider.ts',
            'FCollider.ts',
            'FColliderManager.ts',
            'FightController.ts',
            'FightModel.ts',
            'FightScene.ts',
            'FightUIView.ts',
            'FPolygonCollider.ts',
            'Game.ts',
            'GameAnimi.ts',
            'GameatrCfg.ts',
            'GameCamera.ts',
            'GameEffect.ts',
            'GameSettingCfg.ts',
            'GameSkeleton.ts',
            'GiftPackView.ts',
            'Goods.ts',
            'GoodsUIItem.ts',
            'GridView.ts',
            'GridViewCell.ts',
            'GridViewFreshWork.ts',
            'GTSimpleSpriteAssembler2D.ts',
            'GuideCfg.ts',
            'GuidesController.ts',
            'GuidesModel.ts',
            'index.ts',
            'IOSSdk.ts',
            'ItemController.ts',
            'ItemModel.ts',
            'JUHEAndroid.ts',
            'KnapsackVo.ts',
            'languageCfg.ts',
            'LanguageFun.ts',
            'LaserRadiationBullet.ts',
            'LatticeMap.ts',
            'Launcher.ts',
            'LevelExpCfg.ts',
            'LevelMgr.ts',
            'LifeBar.ts',
            'LifeLabel.ts',
            'LigatureBullet.ts',
            'LoadingController.ts',
            'LoadingModel.ts',
            'LoadingView.ts',
            'LocalStorage.ts',
            'LvInsideCfg.ts',
            'LvOutsideCfg.ts',
            'M20Equipitem.ts',
            'M20EquipitemBlock.ts',
            'M20EquipitemList.ts',
            'M20Gooditem.ts',
            'M20Prop.ts',
            'M20Prop_Equip.ts',
            'M20Prop_Gemstone.ts',
            'M20_PartItem.ts',
            'M20_Pop_EquipInfo.ts',
            'M20_Pop_GameRewardView.ts',
            'M20_Pop_GetBox.ts',
            'M20_Pop_GetEnergy.ts',
            'M20_Pop_Insufficient_Props_Tips.ts',
            'M20_Pop_NewEquipUnlock.ts',
            'M20_Pop_ShopBoxInfo.ts',
            'M20_Pop_ShopBuyConfirm.ts',
            'M20_PrePare_Activity.ts',
            'M20_PrePare_Equip.ts',
            'M20_PrePare_Fight.ts',
            'M20_PrePare_MenuView.ts',
            'M20_PrePare_Shop.ts',
            'M20_ShopPartItem.ts',
            'M20_ShopPartItem_adcoupon.ts',
            'M20_ShopPartItem_box.ts',
            'M20_ShopPartItem_coin.ts',
            'M20_ShopPartItem_daily.ts',
            'M20_ShopPartItem_hero.ts',
            'M20_Shop_HeroItem.ts',
            'M33_FightBuffView.ts',
            'M33_FightScene.ts',
            'M33_FightUIView.ts',
            'M33_Pop_DiffSelectGeneral.ts',
            'M33_Pop_GameEnd.ts',
            'M33_Pop_Revive.ts',
            'M33_TestBox.ts',
            'MapCfg.ts',
            'MBackpackHero.ts',
            'MBRebound.ts',
            'MBRMonster.ts',
            'MBRRole.ts',
            'MCBoss.ts',
            'MCBossState.ts',
            'MCDragoMutilation.ts',
            'MCDragon.ts',
            'MChains.ts',
            'MCPet.ts',
            'MCRole.ts',
            'MiniGameEquipCfg.ts',
            'MiniGameLvCfg.ts',
            'MMGMonster.ts',
            'MMGRole.ts',
            'MMGuards.ts',
            'ModeAllOutAttackController.ts',
            'ModeAllOutAttackModel.ts',
            'ModeBackpackHeroController.ts',
            'ModeBackpackHeroModel.ts',
            'ModeBulletsReboundController.ts',
            'ModeBulletsReboundModel.ts',
            'ModeChainsController.ts',
            'ModeChainsModel.ts',
            'ModeDragonWarController.ts',
            'ModeDragonWarModel.ts',
            'ModeManGuardsController.ts',
            'ModeManGuardsModel.ts',
            'ModePickUpBulletsController.ts',
            'ModePickUpBulletsModel.ts',
            'ModeThrowingKnifeController.ts',
            'ModeThrowingKnifeModel.ts',
            'ModuleLauncher.ts',
            'MonstarTideDragon.ts',
            'Monster.ts',
            'MonsterCfg.ts',
            'MonsterElite.ts',
            'MonsterLvCfg.ts',
            'MonsterState.ts',
            'MonsterTidal.ts',
            'MonsterTidalBoss.ts',
            'MonsterTidalState.ts',
            'MonsterTideDefend.ts',
            'MoreGamesItem.ts',
            'MoreGamesView.ts',
            'MoveEntity.ts',
            'MoveImg.ts',
            'MovingBGAssembler.ts',
            'MovingBGSprite.ts',
            'MTideDefendRebound.ts',
            'MTideDefendRmod.ts',
            'MTKnife.ts',
            'MTKRole.ts',
            'NativeAndroid.ts',
            'NotifyCaller.ts',
            'NotifyListener.ts',
            'NPC.ts',
            'OrganismBase.ts',
            'PayController.ts',
            'PayModel.ts',
            'PayShopCfg.ts',
            'Pet.ts',
            'PetState.ts',
            'PoolListCfg.ts',
            'Pop.ts',
            'ProcessRewardsCfg.ts',
            'PropertyVo.ts',
            'randomNameCfg.ts',
            'RBadgeController.ts',
            'RBadgeModel.ts',
            'RBadgePoint.ts',
            'ReflexBullet.ts',
            'Report.ts',
            'ReportQueue.ts',
            'ResUtil.ts',
            'RewardEvent.ts',
            'Role.ts',
            'RoleCfg.ts',
            'RoleLvCfg.ts',
            'RoleSkillList.ts',
            'RoleState.ts',
            'RoleUnlockCfg.ts',
            'SdkLauncher.ts',
            'SelectAlert.ts',
            'SettingController.ts',
            'SettingModel.ts',
            'SettingView.ts',
            'ShareButton.ts',
            'ShopController.ts',
            'ShopModel.ts',
            'signCfg.ts',
            'SkeletonBullet.ts',
            'SkiilpoolCfg.ts',
            'SkillCfg.ts',
            'SkillController.ts',
            'SkillManager.ts',
            'SkillModel.ts',
            'SkillModule.ts',
            'SoundCfg.ts',
            'TaskCfg.ts',
            'TaskModel.ts',
            'TaskTypeCfg.ts',
            'TestController.ts',
            'TestItem.ts',
            'TestModel.ts',
            'TestView.ts',
            'ThrowBullet.ts',
            'TideDefendController.ts',
            'TideDefendModel.ts',
            'TimeManage.ts',
            'TornadoBullet.ts',
            'TowerAmethystRewardCfg.ts',
            'TowerCfg.ts',
            'TowerCoinRewardCfg.ts',
            'TowerLvCfg.ts',
            'TowerMenuCfg.ts',
            'TrackBullet.ts',
            'TrackItem.ts',
            'TrackManger.ts',
            'ttPostbackCtl.ts',
            'TwoDHorizontalLayoutObject.ts',
            'TwoDLayoutObject.ts',
            'UILauncher.ts',
            'UserVo.ts',
            'Vehicle.ts',
            'VideoButton.ts',
            'VideoIcon.ts',
            'VisibleComponent.ts',
            'WallBase.ts',
            'Weather.ts',
            'WeatherCfg.ts',
            'WebDev.ts',
            'WonderSdk.ts'
        ];
    }

    /**
     * 移动第五阶段的TypeScript文件到output_5目录
     */
    movePhase5OutputFiles() {
        const sourceDir = 'output';
        const targetDir = 'output_5';

        console.log('🚀 开始移动第五阶段脚本转换的TypeScript文件...');

        // 创建目标目录
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
            console.log(`📁 创建目录: ${targetDir}`);
        }

        let successCount = 0;
        let notFoundCount = 0;
        const notFoundFiles = [];

        this.phase5Files.forEach((fileName, index) => {
            const sourcePath = path.join(sourceDir, fileName);
            const targetPath = path.join(targetDir, fileName);

            console.log(`[${index + 1}/${this.phase5Files.length}] 移动: ${fileName}`);

            try {
                if (fs.existsSync(sourcePath)) {
                    // 移动文件（先复制再删除原文件）
                    fs.copyFileSync(sourcePath, targetPath);
                    fs.unlinkSync(sourcePath);
                    console.log(`✅ 移动成功: ${fileName}`);
                    successCount++;
                } else {
                    console.log(`⚠️  文件不存在: ${fileName}`);
                    notFoundFiles.push(fileName);
                    notFoundCount++;
                }
            } catch (error) {
                console.error(`❌ 移动失败: ${fileName} - ${error.message}`);
                notFoundCount++;
            }
        });

        console.log(`\n📊 移动完成统计:`);
        console.log(`  - 成功移动: ${successCount}个`);
        console.log(`  - 未找到文件: ${notFoundCount}个`);
        console.log(`  - 总计: ${this.phase5Files.length}个`);

        if (notFoundFiles.length > 0) {
            console.log(`\n⚠️  未找到的文件列表:`);
            notFoundFiles.forEach((file, index) => {
                console.log(`  ${index + 1}. ${file}`);
            });
        }

        // 检查剩余文件
        this.checkRemainingFiles(sourceDir);

        console.log(`\n✅ 第五阶段TypeScript文件已移动到 ${targetDir} 目录`);
        console.log(`📝 现在 output 目录只包含手动转换的文件，output_5 目录包含脚本转换的文件`);
    }

    /**
     * 检查output目录中剩余的文件
     */
    checkRemainingFiles(outputDir) {
        try {
            const remainingFiles = fs.readdirSync(outputDir)
                .filter(file => file.endsWith('.ts'))
                .sort();

            console.log(`\n📋 ${outputDir} 目录剩余文件: ${remainingFiles.length}个`);
            if (remainingFiles.length > 0) {
                console.log(`剩余文件列表（手动转换的文件）:`);
                remainingFiles.forEach((file, index) => {
                    console.log(`  ${index + 1}. ${file}`);
                });
            }
        } catch (error) {
            console.error(`检查剩余文件时出错: ${error.message}`);
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const mover = new Phase5OutputMover();
    mover.movePhase5OutputFiles();
}

module.exports = Phase5OutputMover;
