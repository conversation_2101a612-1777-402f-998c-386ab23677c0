var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Smoother = require("Smoother");
var $2BaseEntity = require("BaseEntity");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_MoveEntity = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._velocity = cc.v2();
    t._side = cc.v2();
    t._heading = cc.v2(0, 1);
    t._maxSpeed = 250;
    t._maxForce = 400;
    t._timeElapsed = 0;
    t.isSmoother = true;
    t._headingSmoother = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "Picking", {
    get: function () {
      var e;
      return (null === (e = this.property) || undefined === e ? undefined : e.cut.Picking) || 200;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxAllHp", {
    get: function () {
      return this.property.cut.hp;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "velocity", {
    get: function () {
      return this._velocity;
    },
    set: function (e) {
      this._velocity.set(e);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "side", {
    get: function () {
      return this._side;
    },
    set: function (e) {
      this._side = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "heading", {
    get: function () {
      return this._heading;
    },
    set: function (e) {
      if (e.magSqr() < 1e-5) {
        console.error("new heading is zero", this.ID);
      } else {
        this._heading.set(e);
        cc.Vec2.set(this._side, -e.y, e.x);
        if (!this.isSmoother) {
          var t = this._heading.signAngle(cc.Vec2.UP);
          var o = cc.misc.radiansToDegrees(t);
          this.node && (this.node.angle = -o);
        }
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxSpeed", {
    get: function () {
      return this._maxSpeed;
    },
    set: function (e) {
      this._maxSpeed = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxForce", {
    get: function () {
      return this._maxForce;
    },
    set: function (e) {
      this._maxForce = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.timeElapse = function () {
    return this._timeElapsed;
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this._timeElapsed = t;
  };
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.updateDir = function () {
    var e = this._headingSmoother.onUpdate(this._heading);
    this.horDir = e.x > 0 ? -1 : 1;
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this._headingSmoother || (this._headingSmoother = new $2Smoother.Smoother(10));
  };
  _ctor.prototype.tagVehiclesWithinViewRange = function () {};
  _ctor.prototype.addBuff = function () {};
  _ctor.prototype.addBuffByData = function (e, t) {
    undefined === t && (t = 1);
  };
  _ctor.prototype.changeListener = function () {};
  return cc__decorate([ccp_ccclass], _ctor);
}($2BaseEntity.default);
exports.default = def_MoveEntity;