var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_AutoAnimationClip = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.clipImgName = "v1/images/fight/effect/";
    t.clipImgRef = "1-10";
    t.frame_time = 30;
    t.wrapMode = cc.WrapMode.Default;
    t.tag = $2GameSeting.GameSeting.TweenType.Game;
    t.exitAmTime = .1;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    var e = this;
    this.myAm = this.node.getORaddComponent(cc.Animation);
    var t = [];
    var o = this.clipImgRef.split("-");
    for (var i = +o[0]; i < +o[1] + 1; i++) {
      t.push("" + this.clipImgName + i);
    }
    $2Game.Game.Mgr.instance.getAmClip({
      assetName: this.clipImgName,
      amName: "idle",
      path: t,
      frame_time: this.frame_time
    }).then(function (t) {
      t.wrapMode = e.wrapMode;
      e.myAm.addClip(t);
      e.myAm.play("idle");
    });
    this.wrapMode != cc.WrapMode.Loop && this.myAm.on(cc.Animation.EventType.FINISHED, function () {
      cc.tween(e.node).to(e.exitAmTime, {
        opacity: 1
      }).start();
    });
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
  };
  _ctor.prototype.onGameState = function (e) {
    var t;
    var o;
    if (e == $2Game.Game.State.PAUSE) {
      null === (t = this.myAm) || undefined === t || t.pause();
    } else {
      null === (o = this.myAm) || undefined === o || o.resume();
    }
  };
  _ctor.prototype.onEnable = function () {
    if (this.myAm) {
      this.node.opacity = 255;
      this.myAm.play("idle", 0);
      this.changeListener(true);
    }
  };
  _ctor.prototype.onDisable = function () {
    if (this.myAm) {
      cc.Tween.stopAllByTarget(this.node);
      this.myAm.stop();
      this.changeListener(false);
    }
  };
  cc__decorate([ccp_property({
    displayName: "序列帧前缀"
  })], _ctor.prototype, "clipImgName", undefined);
  cc__decorate([ccp_property({
    displayName: "序列帧编号"
  })], _ctor.prototype, "clipImgRef", undefined);
  cc__decorate([ccp_property({
    displayName: "帧率",
    min: 0,
    max: 60,
    slide: true
  })], _ctor.prototype, "frame_time", undefined);
  cc__decorate([ccp_property({
    type: cc.Enum(cc.WrapMode),
    displayName: "是否循环"
  })], _ctor.prototype, "wrapMode", undefined);
  cc__decorate([ccp_property({
    type: cc.Enum($2GameSeting.GameSeting.TweenType),
    displayName: "动画tag"
  })], _ctor.prototype, "tag", undefined);
  cc__decorate([ccp_property({
    displayName: "动画完成消失时间",
    min: 0,
    max: 5,
    slide: true
  })], _ctor.prototype, "exitAmTime", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/AutoAnimationClip")], _ctor);
}(cc.Component);
exports.default = def_AutoAnimationClip;