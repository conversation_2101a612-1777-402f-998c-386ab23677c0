const fs = require('fs');
const path = require('path');

/**
 * 将第五阶段转换的JavaScript文件复制到scripts_5文件夹
 * 这样可以清楚区分哪些是脚本自动转换的，哪些是之前手动转换的
 */
class Phase5FileCopier {
    constructor() {
        this.phase5Files = [
            'ADController.js',
            'ADModel.js',
            'AlertManager.js',
            'Api.js',
            'ArcBullet.js',
            'AssetLoader.js',
            'AutoAmTool.js',
            'AutoAnimationClip.js',
            'AutoFollow.js',
            'AutoScaleComponent.js',
            'BackHeroProp.js',
            'BackpackHeroHome.js',
            'BagModeSkillPoolCfg.js',
            'bagMonsterLvCfg.js',
            'BagShopItemCfg.js',
            'BagSkillCfg.js',
            'BoomerangBullet.js',
            'BottomBarController.js',
            'BottomBarModel.js',
            'BottomBarView.js',
            'BounceBullet.js',
            'BoxLevelExpCfg.js',
            'BronMonsterManger.js',
            'Buff.js',
            'BuffCardItem.js',
            'BuffCfg.js',
            'BuffController.js',
            'BuffList.js',
            'BuffModel.js',
            'BuildModeSkiilpoolCfg.js',
            'Bullet.js',
            'BulletBase.js',
            'BulletEffectCfg.js',
            'BulletVoPool.js',
            'Bullet_Arrow.js',
            'Bullet_FollowTarget.js',
            'Bullet_HitReflex.js',
            'Bullet_Laser.js',
            'Bullet_Ligature.js',
            'Bullet_LigaturePonit.js',
            'Bullet_Path.js',
            'Bullet_RandomMove.js',
            'Bullet_RigidBody.js',
            'ByteDance.js',
            'Cfg.js',
            'CircleBullet.js',
            'Commonguide.js',
            'CompManager.js',
            'config.js',
            'ContinuousBullet.js',
            'CurrencyConfigCfg.js',
            'DialogBox.js',
            'dmmItemCfg.js',
            'dmmRoleCfg.js',
            'Dragon.js',
            'DragonBody.js',
            'dragonPathCfg.js',
            'DropConfigCfg.js',
            'EaseScaleTransition.js',
            'EffectSkeleton.js',
            'Effect_Behead.js',
            'Effect_Behit.js',
            'EnergyStamp.js',
            'EntityDieEffect.js',
            'EquipLvCfg.js',
            'EquipMergeLvCfg.js',
            'EventController.js',
            'EventModel.js',
            'ExchangeCodeView.js',
            'FBoxCollider.js',
            'FCircleCollider.js',
            'FCollider.js',
            'FColliderManager.js',
            'FightController.js',
            'FightModel.js',
            'FightScene.js',
            'FightUIView.js',
            'FPolygonCollider.js',
            'Game.js',
            'GameAnimi.js',
            'GameatrCfg.js',
            'GameCamera.js',
            'GameEffect.js',
            'GameSettingCfg.js',
            'GameSkeleton.js',
            'GiftPackView.js',
            'Goods.js',
            'GoodsUIItem.js',
            'GridView.js',
            'GridViewCell.js',
            'GridViewFreshWork.js',
            'GTSimpleSpriteAssembler2D.js',
            'GuideCfg.js',
            'GuidesController.js',
            'GuidesModel.js',
            'index.js',
            'IOSSdk.js',
            'ItemController.js',
            'ItemModel.js',
            'JUHEAndroid.js',
            'KnapsackVo.js',
            'languageCfg.js',
            'LanguageFun.js',
            'LaserRadiationBullet.js',
            'LatticeMap.js',
            'Launcher.js',
            'LevelExpCfg.js',
            'LevelMgr.js',
            'LifeBar.js',
            'LifeLabel.js',
            'LigatureBullet.js',
            'LoadingController.js',
            'LoadingModel.js',
            'LoadingView.js',
            'LocalStorage.js',
            'LvInsideCfg.js',
            'LvOutsideCfg.js',
            'M20Equipitem.js',
            'M20EquipitemBlock.js',
            'M20EquipitemList.js',
            'M20Gooditem.js',
            'M20Prop.js',
            'M20Prop_Equip.js',
            'M20Prop_Gemstone.js',
            'M20_PartItem.js',
            'M20_Pop_EquipInfo.js',
            'M20_Pop_GameRewardView.js',
            'M20_Pop_GetBox.js',
            'M20_Pop_GetEnergy.js',
            'M20_Pop_Insufficient_Props_Tips.js',
            'M20_Pop_NewEquipUnlock.js',
            'M20_Pop_ShopBoxInfo.js',
            'M20_Pop_ShopBuyConfirm.js',
            'M20_PrePare_Activity.js',
            'M20_PrePare_Equip.js',
            'M20_PrePare_Fight.js',
            'M20_PrePare_MenuView.js',
            'M20_PrePare_Shop.js',
            'M20_ShopPartItem.js',
            'M20_ShopPartItem_adcoupon.js',
            'M20_ShopPartItem_box.js',
            'M20_ShopPartItem_coin.js',
            'M20_ShopPartItem_daily.js',
            'M20_ShopPartItem_hero.js',
            'M20_Shop_HeroItem.js',
            'M33_FightBuffView.js',
            'M33_FightScene.js',
            'M33_FightUIView.js',
            'M33_Pop_DiffSelectGeneral.js',
            'M33_Pop_GameEnd.js',
            'M33_Pop_Revive.js',
            'M33_TestBox.js',
            'MapCfg.js',
            'MBackpackHero.js',
            'MBRebound.js',
            'MBRMonster.js',
            'MBRRole.js',
            'MCBoss.js',
            'MCBossState.js',
            'MCDragoMutilation.js',
            'MCDragon.js',
            'MChains.js',
            'MCPet.js',
            'MCRole.js',
            'MiniGameEquipCfg.js',
            'MiniGameLvCfg.js',
            'MMGMonster.js',
            'MMGRole.js',
            'MMGuards.js',
            'ModeAllOutAttackController.js',
            'ModeAllOutAttackModel.js',
            'ModeBackpackHeroController.js',
            'ModeBackpackHeroModel.js',
            'ModeBulletsReboundController.js',
            'ModeBulletsReboundModel.js',
            'ModeChainsController.js',
            'ModeChainsModel.js',
            'ModeDragonWarController.js',
            'ModeDragonWarModel.js',
            'ModeManGuardsController.js',
            'ModeManGuardsModel.js',
            'ModePickUpBulletsController.js',
            'ModePickUpBulletsModel.js',
            'ModeThrowingKnifeController.js',
            'ModeThrowingKnifeModel.js',
            'ModuleLauncher.js',
            'MonstarTideDragon.js',
            'Monster.js',
            'MonsterCfg.js',
            'MonsterElite.js',
            'MonsterLvCfg.js',
            'MonsterState.js',
            'MonsterTidal.js',
            'MonsterTidalBoss.js',
            'MonsterTidalState.js',
            'MonsterTideDefend.js',
            'MoreGamesItem.js',
            'MoreGamesView.js',
            'MoveEntity.js',
            'MoveImg.js',
            'MovingBGAssembler.js',
            'MovingBGSprite.js',
            'MTideDefendRebound.js',
            'MTideDefendRmod.js',
            'MTKnife.js',
            'MTKRole.js',
            'NativeAndroid.js',
            'NotifyCaller.js',
            'NotifyListener.js',
            'NPC.js',
            'OrganismBase.js',
            'PayController.js',
            'PayModel.js',
            'PayShopCfg.js',
            'Pet.js',
            'PetState.js',
            'PoolListCfg.js',
            'Pop.js',
            'ProcessRewardsCfg.js',
            'PropertyVo.js',
            'randomNameCfg.js',
            'RBadgeController.js',
            'RBadgeModel.js',
            'RBadgePoint.js',
            'ReflexBullet.js',
            'Report.js',
            'ReportQueue.js',
            'ResUtil.js',
            'RewardEvent.js',
            'Role.js',
            'RoleCfg.js',
            'RoleLvCfg.js',
            'RoleSkillList.js',
            'RoleState.js',
            'RoleUnlockCfg.js',
            'SdkLauncher.js',
            'SelectAlert.js',
            'SettingController.js',
            'SettingModel.js',
            'SettingView.js',
            'ShareButton.js',
            'ShopController.js',
            'ShopModel.js',
            'signCfg.js',
            'SkeletonBullet.js',
            'SkiilpoolCfg.js',
            'SkillCfg.js',
            'SkillController.js',
            'SkillManager.js',
            'SkillModel.js',
            'SkillModule.js',
            'SoundCfg.js',
            'TaskCfg.js',
            'TaskModel.js',
            'TaskTypeCfg.js',
            'TestController.js',
            'TestItem.js',
            'TestModel.js',
            'TestView.js',
            'ThrowBullet.js',
            'TideDefendController.js',
            'TideDefendModel.js',
            'TimeManage.js',
            'TornadoBullet.js',
            'TowerAmethystRewardCfg.js',
            'TowerCfg.js',
            'TowerCoinRewardCfg.js',
            'TowerLvCfg.js',
            'TowerMenuCfg.js',
            'TrackBullet.js',
            'TrackItem.js',
            'TrackManger.js',
            'ttPostbackCtl.js',
            'TwoDHorizontalLayoutObject.js',
            'TwoDLayoutObject.js',
            'UILauncher.js',
            'UserVo.js',
            'Vehicle.js',
            'VideoButton.js',
            'VideoIcon.js',
            'VisibleComponent.js',
            'WallBase.js',
            'Weather.js',
            'WeatherCfg.js',
            'WebDev.js',
            'WonderSdk.js'
        ];
    }

    /**
     * 复制第五阶段的文件到scripts_5目录
     */
    copyPhase5Files() {
        const scriptsDir = 'scripts';
        const targetDir = 'scripts_5';

        console.log('🚀 开始复制第五阶段转换的JavaScript文件...');

        // 创建目标目录
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
            console.log(`📁 创建目录: ${targetDir}`);
        }

        let successCount = 0;
        let notFoundCount = 0;
        const notFoundFiles = [];

        this.phase5Files.forEach((fileName, index) => {
            const sourcePath = path.join(scriptsDir, fileName);
            const targetPath = path.join(targetDir, fileName);

            console.log(`[${index + 1}/${this.phase5Files.length}] 复制: ${fileName}`);

            try {
                if (fs.existsSync(sourcePath)) {
                    fs.copyFileSync(sourcePath, targetPath);
                    console.log(`✅ 复制成功: ${fileName}`);
                    successCount++;
                } else {
                    console.log(`⚠️  文件不存在: ${fileName}`);
                    notFoundFiles.push(fileName);
                    notFoundCount++;
                }
            } catch (error) {
                console.error(`❌ 复制失败: ${fileName} - ${error.message}`);
                notFoundCount++;
            }
        });

        console.log(`\n📊 复制完成统计:`);
        console.log(`  - 成功复制: ${successCount}个`);
        console.log(`  - 未找到文件: ${notFoundCount}个`);
        console.log(`  - 总计: ${this.phase5Files.length}个`);

        if (notFoundFiles.length > 0) {
            console.log(`\n⚠️  未找到的文件列表:`);
            notFoundFiles.forEach((file, index) => {
                console.log(`  ${index + 1}. ${file}`);
            });
        }

        console.log(`\n✅ 第五阶段文件已复制到 ${targetDir} 目录`);
        console.log(`📝 这些文件是通过脚本自动转换的，便于区分手动转换的文件`);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const copier = new Phase5FileCopier();
    copier.copyPhase5Files();
}

module.exports = Phase5FileCopier;
