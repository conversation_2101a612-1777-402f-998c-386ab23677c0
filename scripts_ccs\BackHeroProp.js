var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc.v2(cc.winSize.width / 2, cc.winSize.height / 2);
var def_BackHeroProp = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._logicTime = .03;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "packView", {
    get: function () {
      return this.game.packView;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "heroHome", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "roleCfg", {
    get: function () {
      return this.master.roleCfg;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "equipCfg", {
    get: function () {
      return this.master.equipCfg;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mergeCfg", {
    get: function () {
      return this.master.mergeCfg;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "haedPosition", {
    get: function () {
      return this.heroHome.haedPosition;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bodyPosition", {
    get: function () {
      return this.heroHome.bodyPosition;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "extraProperty", {
    get: function () {
      return this.master.extraProperty;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    this.node.changeListener(t, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onSkillEnd, this);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    this.entityType = $2BaseEntity.EntityType.Pet;
    this.campType = $2BaseEntity.CampType.One;
  };
  _ctor.prototype.set = function (e) {
    var t;
    var o;
    var i;
    var n;
    var r;
    var a = this;
    this.master = e;
    this.property.set(this.master.property.cut);
    this.skillMgr.clearAll();
    this.buffMgr.clearBuff();
    this.forwardDirection.setVal(0, 1);
    null === (t = this.mySkeleton) || undefined === t || t.reset(null);
    145 == this.mergeCfg.id && $2Manager.Manager.loader.loadSpine("bones/skill/fx_axe6", this.game.gameNode).then(function (e) {
      a.mySkeleton.reset(e);
      a.setAnimation("idle", true);
    });
    null === (o = this.master.skillMgr) || undefined === o || o.skills.forEach(function (e) {
      var t = a.skillMgr.add(e.skillCfg.id, true);
      a.master.mergeCfg.bulletId && (t.skillCfg.bulletId = t.cutVo.bulletId = a.master.mergeCfg.bulletId);
      t.skillCfg.barrangeSpeed += $2Game.Game.random(0, 100);
      t.cutVo.barrangeSpeed = t.skillCfg.barrangeSpeed;
    });
    null === (i = this.master.equipCfg.buff) || undefined === i || i.forEach(function (e) {
      a.buffMgr.add(e);
    });
    null === (n = this.master.mergeCfg.buff) || undefined === n || n.forEach(function (e) {
      a.buffMgr.add(e);
    });
    this.heroHome.buffMgr.bufflist.forEach(function (e) {
      a.addBuffByData(e.cutVo, e.buffLayer);
    });
    null === (r = this.master.buffMgr) || undefined === r || r.bufflist.forEach(function (e) {
      a.addBuffByData(e.cutVo, e.buffLayer);
    });
    this.buffMgr.use(118002, false, function (e) {
      a.addArmor(a.property.cut.hp * e.attrMap.getor($2GameatrCfg.GameatrDefine.addshieldper, 0), false);
    });
  };
  _ctor.prototype.treat = function (e) {
    this.heroHome.treat(e);
  };
  _ctor.prototype.addArmor = function (e, t) {
    undefined === t && (t = true);
    this.heroHome.addArmor(e, t);
  };
  _ctor.prototype.checkBuffCanAdd = function (e) {
    return 1 != e.trialObject && !(e.skillId && !$2GameUtil.GameUtil.hasIntersection(e.skillId, this.skillMgr.skillIDs) && !$2GameUtil.GameUtil.hasIntersection(e.skillId, [13001]));
  };
  _ctor.prototype.addBuff = function (t, o) {
    undefined === o && (o = 1);
    var i = $2Cfg.Cfg.Buff.get(t);
    if (i && this.checkBuffCanAdd(i)) {
      return e.prototype.addBuff.call(this, t, o);
    }
  };
  _ctor.prototype.addBuffByData = function (t, o) {
    undefined === o && (o = 1);
    if (this.checkBuffCanAdd(t)) {
      return e.prototype.addBuffByData.call(this, t, o);
    }
  };
  Object.defineProperty(_ctor.prototype, "curHp", {
    get: function () {
      return this.master.curHp;
    },
    set: function (e) {
      this.master.curHp = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onSkill = function (e, t) {
    var o = this;
    if (![1001, 1006, 1009].includes(this.master.equipCfg.equipId)) {
      this.heroHome.onSkill(e, t);
      this.game.propSkilling.add(this.ID);
      this.delayByGame(function () {
        o.game.propSkilling.delete(o.ID);
      }, .1);
      145 == this.mergeCfg.id && cc.tween(this.mySkeleton.node).to(.1, {
        scale: 1.1
      }).set({
        opacity: 0
      }).delay(1).to(.1, {
        scale: 1,
        opacity: 255
      }).start();
    }
  };
  _ctor.prototype.onSkillEnd = function () {};
  _ctor.prototype.onKill = function (e) {
    this.node.emit($2ListenID.ListenID.Fight_Kill, e);
    this.heroHome.onKill(e);
  };
  Object.defineProperty(_ctor.prototype, "canFire", {
    get: function () {
      return 0 == this.game.propSkilling.size;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.unuse = function () {
    e.prototype.unuse.call(this);
    $2GameUtil.GameUtil.deleteArrItem(this.heroHome.petList, this);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2OrganismBase.default);
exports.default = def_BackHeroProp;